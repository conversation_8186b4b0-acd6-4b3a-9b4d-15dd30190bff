import os
import re
import numpy as np
import matplotlib.pyplot as plt
from main import secretary_problem
from learned_dynkin import learned_dynkin_algorithm
from robustness_test import Algorithm
from additive_pegging import additive_pegging_algorithm
from ordered_additive_pegging import ordered_additive_pegging_algorithm
import random
from multiprocessing import Pool, cpu_count

# --- 1. Style Setup (for a publication-quality look) ---
plt.rcParams.update({
    "font.size": 28,          # General font size
    "axes.titlesize": 36,     # Title font size
    "axes.labelsize": 32,     # Axis label font size
    "xtick.labelsize": 16,    # X-tick label font size (made smaller)
    "ytick.labelsize": 24,    # Y-tick label font size
    "legend.fontsize": 14,    # Legend font size (made smaller)
    "font.family": "serif",   # Use a serif font
    # "text.usetex": True,    # Uncomment if you have a LaTeX installation
})

def run_single_test(test_filepath, num_runs=1):
    """
    Runs a single, specified test case for all algorithms.

    Args:
        test_filepath (str): The path to the test case TXT file.
        num_runs (int): The number of times to run the test to find the expected value.
    """
    # Load the test case data from .txt file
    with open(test_filepath, 'r') as f:
        lines = f.readlines()

    N = int(lines[0].strip())
    v_actual_list = [float(v) for v in lines[1].strip().split()]
    v_predictions_list = [float(v) for v in lines[2].strip().split()]

    # Augment to prevent duplicates by adding small random noise
    v_actual_list = [v + random.uniform(1e-8, 1e-4) for v in v_actual_list]
    v_predictions_list = [v + random.uniform(1e-8, 1e-4) for v in v_predictions_list]

    v_actual = {i: v_actual_list[i] for i in range(N)}
    v_predictions = {i: v_predictions_list[i] for i in range(N)}

    # Define the algorithms to be tested
    algorithms = [
        Algorithm("Ordered Learned Dynkin", secretary_problem),
        Algorithm("Ordered Additive Pegging", ordered_additive_pegging_algorithm),
        Algorithm("Learned Dynkin", learned_dynkin_algorithm),
        Algorithm("Additive Pegging", additive_pegging_algorithm),
        
    ]

    # Run the test for each algorithm
    results = {}
    for alg in algorithms:
        hired_values = []
        for _ in range(num_runs):
            # The same v_actual and v_predictions are used for each run
            hired_candidate, _ = alg.run(v_predictions, v_actual)
            if hired_candidate == -1:
                hired_values.append(0)
            else:
                ratio = v_actual[hired_candidate] / max(v_actual.values())
                hired_values.append(ratio)

        results[alg.name] = np.mean(hired_values)

    return results

def run_test_file_wrapper(args):
    """
    Wrapper function for parallel execution of test files.
    """
    test_file, num_runs = args
    res = run_single_test(test_file, num_runs=num_runs)
    return run_single_test(test_file, num_runs=num_runs)

def run_runner_for_test_dir(test_dir):
    """
    Run the runner logic for a specific test directory and return results.
    """
    print(f"\n=== Running tests for {test_dir} ===")

    all_files = sorted([os.path.join(test_dir, f) for f in os.listdir(test_dir) if f.endswith('.txt')])

    # Group files by their base name (e.g., 'case_01_decoyeps0.0')
    file_groups = {}
    for f in all_files:
        base_name_match = re.match(r'(.+)_(\d+)\.txt$', os.path.basename(f))
        if base_name_match:
            base_name = base_name_match.group(1)
            if base_name not in file_groups:
                file_groups[base_name] = []
            file_groups[base_name].append(f)

    # Data collection for plotting
    eps_values = []
    final_results_by_alg = {}

    print(f"Found {len(file_groups)} groups of test cases in '{test_dir}'.")

    for base_name, file_list in file_groups.items():
        # Extract eps value from the base name
        match = re.search(r'eps(\d+\.?\d*)', base_name)
        if not match:
            print(f"Warning: Could not find eps value in group {base_name}")
            continue
        try:
            eps = float(match.group(1))
            eps_values.append(eps)
            print(f"  Processing Group: {base_name} (Epsilon: {eps})")
        except:
            eps_values.append(0)
            print(f"Warning: Could not convert eps value to float in group {base_name}")

        # Run tests for all files in the group and collect results (parallelized)
        group_results_by_alg = {}
        num_runs_for_test = 1
        if test_dir == "custom_test_cases":
            num_runs_for_test = 10000

        # Prepare arguments for parallel execution
        test_args = [(test_file, num_runs_for_test) for test_file in file_list]

        # Use multiprocessing to run tests in parallel
        num_processes = min(cpu_count(), len(file_list))
        with Pool(processes=num_processes) as pool:
            results_list = pool.map(run_test_file_wrapper, test_args)

        # Collect results from parallel execution
        for results in results_list:
            for alg_name, value in results.items():
                if alg_name not in group_results_by_alg:
                    group_results_by_alg[alg_name] = []
                group_results_by_alg[alg_name].append(value)

        # For each algorithm, find the mean result from the group
        mean_results = {}
        for alg_name, values in group_results_by_alg.items():
            mean_results[alg_name] = sum(values) / len(values)

        # Append the mean results to the final plot data
        for alg_name, mean_value in mean_results.items():
            if alg_name not in final_results_by_alg:
                final_results_by_alg[alg_name] = []
            final_results_by_alg[alg_name].append(mean_value)

    # Sort results based on eps_values for correct plotting
    sorted_indices = np.argsort(eps_values)
    sorted_eps_values = np.array(eps_values)[sorted_indices]
    for alg_name in final_results_by_alg:
        final_results_by_alg[alg_name] = np.array(final_results_by_alg[alg_name])[sorted_indices]

    return sorted_eps_values, final_results_by_alg

if __name__ == '__main__':
    # Define the test directories for the runners we want to include
    test_directories = {
        'almost_constant_test_cases': 'ALMOST CONSTANT',
        'uniform_test_cases': 'UNIFORM',
        #'adversarial_test_cases': 'ADVERSARIAL',
        'three_tier_deception_cases': 'DECEPT', # Using three_tier_deception_cases for decept
        'custom_test_cases': 'ROBUST'
    }

    # Check which directories exist
    available_dirs = {}
    for test_dir, title in test_directories.items():
        if os.path.exists(test_dir):
            available_dirs[test_dir] = title
            print(f"Found directory: {test_dir}")
        else:
            print(f"Warning: Directory {test_dir} not found, skipping...")

    if not available_dirs:
        print("No test directories found! Please check that the test case directories exist.")
        exit(1)

    # Run all available runners and collect results
    all_results = {}
    for test_dir, title in available_dirs.items():
        try:
            eps_values, results_by_alg = run_runner_for_test_dir(test_dir)
            all_results[title] = {
                'eps_values': eps_values,
                'results_by_alg': results_by_alg
            }
        except Exception as e:
            print(f"Error running tests for {test_dir}: {e}")
            continue

    if not all_results:
        print("No results collected! Please check the test directories and files.")
        exit(1)

    # --- 3. Plotting ---
    # Create a figure and a set of subplots in a 2x2 grid
    num_plots = len(all_results)
    fig, axs = plt.subplots(2, 2, figsize=(12, 12), sharey=True)

    # Flatten the axes array for easier indexing
    axs = axs.flatten()

    # Define plotting styles for each algorithm
    # Map the algorithm names from the runners to display names
    algorithm_mapping = {
        'Ordered Learned Dynkin': 'ORDERED-LEARNED-DYNKIN',
        'Learned Dynkin': 'LEARNED-DYNKIN',
        'Additive Pegging': 'ADDITIVE-PEGGING',
        'Ordered Additive Pegging': 'ORDERED-ADDITIVE-PEGGING',
    }

    styles = {
        'ORDERED-LEARNED-DYNKIN':   {'marker': '^', 'linestyle': '-', 'color': 'tab:blue', 'ms': 12},
        'LEARNED-DYNKIN':     {'marker': 'v', 'linestyle': '-.', 'color': 'tab:orange', 'ms': 12},
        'ADDITIVE-PEGGING':             {'marker': 'o', 'linestyle': ':', 'color': 'tab:green', 'ms': 12},
        'ORDERED-ADDITIVE-PEGGING': {'marker': '*', 'linestyle': '--', 'color': 'tab:red', 'ms': 14},
    }

    # Plot each test directory's results
    for i, (title, data) in enumerate(all_results.items()):
        ax = axs[i]
        eps_values = data['eps_values']
        results_by_alg = data['results_by_alg']

        # Plot each algorithm's data on the current axis
        for alg_name, values in results_by_alg.items():
            # Map algorithm name to display name
            display_name = algorithm_mapping.get(alg_name, alg_name)
            if display_name in styles:
                style = styles[display_name]
                ax.plot(eps_values, values,
                        label=display_name,
                        marker=style['marker'],
                        linestyle=style['linestyle'],
                        color=style['color'],
                        markersize=style['ms'],
                        linewidth=2)

        # --- 4. Customizing Axes and Titles ---
        ax.set_title(title)

        # Set the y-axis limits and ticks for all plots
        ax.set_ylim(0, 1.05)
        ax.set_yticks(np.arange(0, 1.1, 0.2))

        # Show eps values on x-axis
        if title != "ROBUST":
            ax.set_xlabel('Epsilon (ε)')
        else:
            ax.set_xlabel('Added candidates')

        # Set x-axis ticks to show eps values with better spacing
        if len(eps_values) > 8:
            # If too many values, show fewer ticks to prevent overlap
            step = max(1, len(eps_values) // 6)
            tick_indices = range(0, len(eps_values), step)
            selected_ticks = [eps_values[i] for i in tick_indices]
            ax.set_xticks(selected_ticks)
            # Rotate labels if still crowded
            ax.tick_params(axis='x', rotation=45)
        else:
            ax.set_xticks(eps_values)
            if len(eps_values) > 5:
                ax.tick_params(axis='x', rotation=45)

        # Add grid lines for better readability
        ax.grid(alpha=0.3)

    # Hide any unused subplots
    for i in range(num_plots, 4):
        axs[i].set_visible(False)

    # --- 5. Final Touches (Labels, Legend, Layout) ---

    # Set the Y-axis label for the left column subplots
    axs[0].set_ylabel('Competitive ratio')
    axs[2].set_ylabel('Competitive ratio')

    # Add the legend only to the first subplot positioned at bottom left
    axs[0].legend(loc='lower left', bbox_to_anchor=(0.02, 0.02), framealpha=0.9)

    # Adjust the spacing between subplots
    plt.subplots_adjust(wspace=0.15, hspace=0.3)

    # Save and display the plot
    plt.savefig("combined_results.png", dpi=300, bbox_inches='tight')
    print(f"\nCombined plot saved to combined_results.png")
    plt.show()