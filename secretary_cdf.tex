\documentclass{article}
\usepackage{amsmath} % For math symbols like \mathbb
\usepackage{amssymb,amsfonts} % For math symbols like \mathbb
\usepackage{algorithm}
\usepackage{algpseudocode} % Load algorithmicx and the pseudocode style
\newtheorem{theorem}{Theorem}
\newtheorem{lemma}[theorem]{Lemma}
\newtheorem{definition}{Definition}

\newcommand{\Prob}{\mathbb{P}}
\newcommand{\E}{\mathbb{E}}
\newcommand{\optp}{\text{opt}_p} % Candidate with highest prediction
\newcommand{\vstar}{v^*} % Maximum actual value

% if you need to pass options to natbib, use, e.g.:
%     \PassOptionsToPackage{numbers, compress}{natbib}
% before loading neurips_2018

% ready for submission
% \usepackage{neurips_2018}

% to compile a preprint version, e.g., for submission to arXiv, add add the
% [preprint] option:
%     \usepackage[preprint]{neurips_2018}

% to compile a camera-ready version, add the [final] option, e.g.:
     \usepackage[preprint]{neurips_2018}

% to avoid loading the natbib package, add option nonatbib:
%     \usepackage[nonatbib]{neurips_2018}

\usepackage[utf8]{inputenc} % allow utf-8 input
\usepackage[T1]{fontenc}    % use 8-bit T1 fonts
\usepackage{hyperref}       % hyperlinks
\usepackage{url}            % simple URL typesetting
\usepackage{booktabs}       % professional-quality tables
\usepackage{amsfonts}       % blackboard math symbols
\usepackage{nicefrac}       % compact symbols for 1/2, etc.
\usepackage{microtype}      % microtypography
\usepackage{amsmath} % Required for cases environment

\title{Secretary problem with predictions and ordering}

% The \author macro works with any number of authors. There are two commands
% used to separate the names and addresses of multiple authors: \And and \AND.
%
% Using \And between authors leaves it to LaTeX to determine where to break the
% lines. Using \AND forces a line break at that point. So, if LaTeX puts 3 of 4
% authors names on the first line, and the last on the second line, try using
% \AND instead of \And before the third author name.

\author{%
  Kang Yiming\\
  % examples of more authors
  % \And
  % Coauthor \\
  % Affiliation \\
  % Address \\
  % \texttt{email} \\
  % \AND
  % Coauthor \\
  % Affiliation \\
  % Address \\
  % \texttt{email} \\
  % \And
  % Coauthor \\
  % Affiliation \\
  % Address \\
  % \texttt{email} \\
  % \And
  % Coauthor \\
  % Affiliation \\
  % Address \\
  % \texttt{email} \\
}

\begin{document}
% \nipsfinalcopy is no longer used

\maketitle

\begin{abstract}
  The abstract paragraph should be indented \nicefrac{1}{2}~inch (3~picas) on
  both the left- and right-hand margins. Use 10~point type, with a vertical
  spacing (leading) of 11~points.  The word \textbf{Abstract} must be centered,
  bold, and in point size 12. Two line spaces precede the abstract. The abstract
  must be limited to one paragraph.
\end{abstract}
\section{Motivation}
In the original problem, we are given no information of the candidates. Hence, no matter how we order the candidates, it will still be essentially random. However, in the context where we are given predicted values of the candidates all at once at the start, it is reasonable to assume that we have some control over the order that we interview them, and hence this is a natural extension. For example, a company, upon receiving resumes, can assign a predicted value to each candidate and interview them in the order proposed in this algorithm.
\section{Algorithm Motivation}
Consider the algorithm where we are able to arbitrarily fix the order of the candidates. Then, the worst-case robustness(where predictions are entirely random) is still bounded by the classical Dynkin algorithm, achieving ratio 1/e. We will proceed to show that allowing this restriction will improve the robustness of current the algorithm proposed previously of 0.215 (https://arxiv.org/pdf/2306.08340)



\section{Algorithm}
Our algorithm follows largely from the algorithm proposed by Fujii and Yoshida. 

Let $\hat{i} = \arg\max \left( \hat{v}(i) \right)$

then $T=t_{\hat{i}}$ will be defined as follows:
\[
T =
\begin{cases}
    \tau & \text{with probability } p \\
    1 & \text{otherwise}
\end{cases}
\]
while the arrival timing of all other candidates are randomly uniform in $[0,1]$

\begin{algorithm}[H]
\caption{Ordered Learned Dynkin}\label{alg:learned_dynkin}
\begin{algorithmic}[1] % The [1] enables line numbering
\Require Time $\tau$, Threshold $\theta$, Predictions $\hat{v} : \mathcal{N} \to \mathbb{R}$
    \Statex % Adds a small vertical space after Require if desired

    \State $\hat{\imath} \in argmax_{i \in \mathcal{N}} \hat{v}(i)$ 
    \ForAll{$i \in \mathcal{N} \setminus \{\hat{i}\}$} % Use \setminus, ensure loop exclusion is intended
        \State Let $t_i \sim U(0,1)$ \Comment{Draw time/cost for candidate $i \neq \hat{i}$}
        % ... other steps inside the loop for candidate i ...
    \EndFor
        
    \State Let $ t_{\hat{i}}  \sim T$
    
    \State $\text{mode} \leftarrow \text{Prediction}$ \Comment{Initialize mode}

    \ForAll{candidate $i \in \mathcal{N}$ in order of arrival time $t_i$}
        \Statex 
        \If{$|1 - \frac{\hat{v}(i)}{v(i)}| > \theta$} 
            \State $\text{mode} \leftarrow \text{Secretary}$ 
        \EndIf
        \Statex 
        \If{$\text{mode} = \text{Prediction}$ \textbf{and} $i = \hat{\imath}$} 
            \State Hire $i$
            \State \Return % Assuming the process stops after hiring one candidate
        \EndIf
        \Statex 
        \If{$\text{mode} = \text{Secretary}$ \textbf{and} $t_i > \tau$ \textbf{and} $i$ is the best so far} 
            \State Hire $i$
            \State \Return % Assuming the process stops after hiring one candidate
        \EndIf
        \If{$i$ is the last element in $\mathcal{N}$} 
            \State Hire $i$
            \State \Return % Assuming the process stops after hiring one candidate
        \EndIf
    \Statex 
    % \State No candidate hired. (Add if needed)
\end{algorithmic}
\end{algorithm}
\section{T must not be constant in this algorithm for it to be competitive}
\subsection{$P(T>\tau)>0$}
Suppose otherwise, T is a constant s.t. $T<\tau$

Then, Case 5.1 will give a competitive ratio of 0 because $i*$ cannot be selected.
\subsection{$P(T<\tau)>0$}
Suppose otherwise, T is a constant s.t. $T>\tau$

Then, Case 5.4 will give a competitive ratio of 0 when $M=\{\hat{i}\}$ and $\hat{i}$ is the second largest element besides $i^*$

If $T<t_{i^*}$, then $\hat{i}$ will be selected at T. 

If $T>t_{i^*}$, then the algorithm will still be in prediction mode at $t_{i^*}$

Hence T must be a random variable.

\section{Cases}
\subsection{In the case where $\hat{i}=i^* \in M$}
\subsubsection{$T>\tau$}
Since $\hat{\imath} \in M$, the algorithm never hires a candidate when the mode is \textit{Prediction}. Hence, it is sufficient to consider the case where the algorithm hires a candidate when the mode is \textit{Secretary}. 

When we fix $T \in [\tau, 1]$, the algorithm hires $i^*$ if the best candidate in $[0, t^*)$ appears before $\tau$ (this is a sufficient but not a necessary condition). If we fix the set of candidates before $t^*$, the best candidate before $t^*$ is also fixed. Since the time when this candidate appears conforms to the uniform distribution on $[0, t^*)$, the probability that this time is earlier than $\tau$ is $\tau / t^*$. 

By taking the expectation over $T$, we can show that the success probability is at least
\[
\int_0^1P(T=t)\frac{\tau}{t}dt
\]



\subsection{In the case where $\hat{\imath} = i^* \notin M$.}
\subsubsection{$T>\tau$}
The algorithm hires $\hat{\imath}$ in the Prediction mode when $\hat{\imath}$ appears before all the candidates in $M$. Since $M \cup \{\hat{\imath}\}$ appear in random order, this probability is $A=(1-T)^m$, where $m = |M|$. 

Next, we consider the probability that the algorithm hires $i^*$ in the Secretary mode. It is $B=(1-(1-T)^m)\frac{\tau}{T}$

Hence, $A+B>=\boxed{
\int_0^1P(T=t)\frac{\tau}{t}dt
}$

\subsection{In the case where $\hat{\imath} \neq i^*$, $\hat{\imath} \in M$, and $i^* \in M$.}
Since $\hat{\imath} \in M$, the algorithm switches to the Secretary mode when observing $\hat{\imath}$. The algorithm hires $i^*$ if $t^* \in [\tau, 1]$ and any candidate before $i^*$ is not hired. Hence, a sufficient condition for hiring $i^*$ is that $t^* \in [\tau, 1]$ and the best candidate in $[0, t^*)$ appears before $\tau$. Note that the algorithm might successfully hire $i^*$ if the algorithm observes the best candidate in $[0, t^*)$ after $\tau$ in the Prediction mode, but we ignore this probability. The probability that the sufficient condition holds can be computed in the same way as (i) as $\int_{\tau}^{1} \frac{\tau}{t^*} dt^* = \boxed{\tau \ln \frac{1}{\tau}}$.

\subsection{In the case where $\hat{i} \neq i^*, \hat{i} \in M, i^* \notin M$.}
%Consider the cases where i* is before T or after T

%Case 1: Before T:
%\[
%\int_\tau^T (1 - (1 - t^*)^{m-1}) \frac{\tau}{ t^*} dt^*
%\]
%Case 2: After T:
%\[
%\int_T^1 \frac{\tau}{ t^*} dt^*
%\]
%0 if $\hat{i}$ is the second largest element!
Let $q=m-1$ be number of elements in M excluding $\hat{i}$. Ratio is worst when q=0. Then, in that case, we need $T<t^*$ and the best element that arrives before $t^*$ to arrive before $\tau$. Since the best element could be adversarilly chosen to follow $T$ or $U(0,1)$ distribution, we take the min of the two choices. 
\[
\phi>=\int_{\tau}^1 P(T<=t^*)min(P(T<=\tau|T<=t^*),\frac{\tau}{t^*})dt^*
\]



\subsection{In the case where $\hat{i} \neq i^*$, $\hat{i} \notin M$, and $i^* \in M$}
\subsubsection{Case 1: $T>=t^*$}
General case of m:
the only way for i* to be selected is if i* is the first element to appear in M, OR else, if it's not the first element in M, then the max element appearing before him is before $\tau$
\[
\phi>=\int_\tau^1 P(t^*<=T)(1 - t^*)^{m-1})dt^* + \int_\tau^1 P(t^*<=T)(1-(1 - t^*)^{m-1})\frac{\tau}{ t^*} dt^*
\]

In the case where $T<t^*$, then we need $t_m<T$ and $t_b<\tau$, where $t_b$ is defined as the arrival time of the best element before $t^*$

For fixed $t^*$ and m, consider $i_b$ to be the maximum value before $t^*$. then $ i_b=\hat{i}$ or $i_b\in M$ or $i_b \notin M \cup \{\hat{i}\}$
\[
\phi>=\int_\tau^1P(T<t^*)\int_0^\tau((m-1)*(1-t_M)^{m-2}*P(T>t_m|T<t^*) * \min(P(T<\tau | t_m<T<t^*),\frac{\tau-t_m}{t^*-t_m})dt
\]
However, for $m<=2$, we achieve a better ratio of
\[
\phi=\int_\tau^1P(T<t^*)\int_0^\tau((m-1)*(1-t_M)^{m-2}*P(T>t_m|T<t^*) * \min(P(T<\tau | t_m<T<t^*),\frac{\tau}{t^*})dt
\]
\subsection{In the case where $\hat{i} \neq i^*$, $\hat{i} \notin M$, and $i^* \notin M$.}

\subsubsection{Hiring $\hat{i}$}
It is hired if it comes before all m elements. Hence, the contribution to the answer is 
\[
(1-T)^m\frac{1-\theta}{1+\theta}
\]
Another case that we can force the program to hire $\hat{i}$ is if $t^*<\tau$ and $T=1$. Then, we will definitely be forced to hire the predicted max secretary. This case contributes to the answer
\[
\tau P(T=1)\frac{1-\theta}{1+\theta}
\]
\subsubsection{$T>t^*$ and hiring $i^*$}
then, we need an element in M to activate the secretary mode by appearing before $t^*$
\[
\phi=\int_\tau^1P(t^*<T)(1-(1-t^*)^m)\frac{\tau}{t}dt
\]

\subsubsection{$T<t^*$ and hiring $i^*$}
The pdf of $t_m$ is given by $m*(1-t_m)^{m-1}$

For fixed $t^*$ and m, consider $i_b$ to be the maximum value before $t^*$. then $ i_b=\hat{i}$ or $i_b\in M$ or $i_b \notin M \cup \{\hat{i}\}$
\[
\phi=\int_\tau^1P(T<t^*)\int_0^\tau(m*(1-t_M)^{m-1}*P(T>t_m|T<t^*) * \min(P(T<\tau | t_m<T<t^*),\frac{\tau-t_m}{t^*-t_m})dt
\]
However, for $m<=1$, we achieve a better ratio of
\[
\phi=\int_\tau^1P(T<t^*)\int_0^\tau((m-1)*(1-t_M)^{m-2}*P(T>t_m|T<t^*) * \min(P(T<\tau | t_m<T<t^*),\frac{\tau}{t^*})dt
\]



\section{Finding the distribution of T}



\section{Hardness}

\begin{enumerate}
    \item \textbf{Baseline:} The optimal classical secretary algorithm (no predictions) achieves a success probability of $\approx 1/e$.

    \item \textbf{Constraint:} The algorithm $\A$ must be 1-consistent: if actual values match predicted values ($v(i)=p(i)$ for all $i$), it \emph{must} select the candidate $\optp$ with the highest predicted value.

    \item \textbf{Conflict Scenario:} Consider an input instance where:
        \begin{itemize}
            \item The actual best candidate $k$ (with value $\vstar$) is \emph{not} the predicted best $\optp$ (i.e., $k \neq \optp$).
            \item The classical algorithm \emph{would} select $k$ (it arrives after the rejection phase and is the best seen so far).
            \item Crucially, the actual values observed up to the arrival of $k$ perfectly match the predicted values for those candidates ($v(o_j)=p(o_j)$ for observed $j$ up to $k$'s arrival).
        \end{itemize}

    \item \textbf{Forced Failure:} When candidate $k$ arrives, the observed history is indistinguishable from the beginning of the potential all-accurate instance (where $v(i)=p(i)$ always). To satisfy 1-consistency for that potential instance (where it must eventually pick $\optp$, who hasn't arrived yet), $\A$ \emph{cannot} select $k$ now. It must reject $k$.

    \item \textbf{Conclusion:} By rejecting the actual best candidate $k$ ($\vstar$) in this scenario, $\A$ fails on an instance where the classical algorithm would succeed. Since such conflict scenarios exist with non-zero probability, the maximum success probability (competitive ratio $\alpha$) for $\A$ must be strictly less than the classical optimum of $\approx 1/e$.
\end{enumerate}
\subsection{Other directions}
1. Region of smoothness until error is so big that it just becomes robustness? (Tradeoff between smoothness and robustness)

2. Consider the predicted value of the second largest element with respect to the largest element. 
If they are around the same, we can fulfill both $T=\tau$ and $T=1$ at the same time?

If they are different enough, then Case 6 will be impossible, and hence, we will be able to get another set of hyperparameters for that case which can better our competitive ratio

Prove that T being a bimodel distribution with values tau and 1 is optimal? 
\section{Analysis relative sizes of largest predicted element and the next largest predicted element}
Let $j$ be the index of the element such that $\hat{v}(j)$ is the second largest predicted element.

let $R=\frac{\hat{v}(j)}{\hat{v}(i)}$
if R<0.25, then case (6) cannot exist which means we can ignore it.

\end{document}
