\documentclass{article}
\usepackage{ifthen}
\usepackage{amsmath} % For math symbols like \mathbb
\usepackage{amssymb} % For math symbols like \mathbb
\usepackage{algorithm}
\usepackage{algorithmic}
\usepackage{algpseudocode} % Load algorithmicx and the pseudocode style
% if you need to pass options to natbib, use, e.g.:
%     \PassOptionsToPackage{numbers, compress}{natbib}
% before loading neurips_2018

% ready for submission
% \usepackage{neurips_2018}

% to compile a preprint version, e.g., for submission to arXiv, add add the
% [preprint] option:
%     \usepackage[preprint]{neurips_2018}

% to compile a camera-ready version, add the [final] option, e.g.:
     \usepackage[preprint]{neurips_2018}

% to avoid loading the natbib package, add option nonatbib:
%     \usepackage[nonatbib]{neurips_2018}

\usepackage[utf8]{inputenc} % allow utf-8 input
\usepackage[T1]{fontenc}    % use 8-bit T1 fonts
\usepackage{hyperref}       % hyperlinks
\usepackage{url}            % simple URL typesetting
\usepackage{booktabs}       % professional-quality tables
\usepackage{amsfonts}       % blackboard math symbols
\usepackage{nicefrac}       % compact symbols for 1/2, etc.
\usepackage{microtype}      % microtypography
\usepackage{amsmath} % Required for cases environment
\newcommand{\ihat}{\hat{\imath}}
\newcommand{\Ncal}{\mathcal{N}}
\newcommand{\Rbb}{\mathbb{R}}
\newcommand{\Udist}{\mathcal{U}} % Or just use U
\title{Secretary problem with predictions and ordering}

% The \author macro works with any number of authors. There are two commands
% used to separate the names and addresses of multiple authors: \And and \AND.
%
% Using \And between authors leaves it to LaTeX to determine where to break the
% lines. Using \AND forces a line break at that point. So, if LaTeX puts 3 of 4
% authors names on the first line, and the last on the second line, try using
% \AND instead of \And before the third author name.

\author{%
  Kang Yiming\\
  % examples of more authors
  % \And
  % Coauthor \\
  % Affiliation \\
  % Address \\
  % \texttt{email} \\
  % \AND
  % Coauthor \\
  % Affiliation \\
  % Address \\
  % \texttt{email} \\
  % \And
  % Coauthor \\
  % Affiliation \\
  % Address \\
  % \texttt{email} \\
  % \And
  % Coauthor \\
  % Affiliation \\
  % Address \\
  % \texttt{email} \\
}

\begin{document}
% \nipsfinalcopy is no longer used

\maketitle

\begin{abstract}
  The abstract paragraph should be indented \nicefrac{1}{2}~inch (3~picas) on
  both the left- and right-hand margins. Use 10~point type, with a vertical
  spacing (leading) of 11~points.  The word \textbf{Abstract} must be centered,
  bold, and in point size 12. Two line spaces precede the abstract. The abstract
  must be limited to one paragraph.
\end{abstract}
\section{Motivation}
In the original problem, we are given no information of the candidates. Hence, no matter how we order the candidates, it will still be essentially random. However, in the context where we are given predicted values of the candidates all at once at the start, it is reasonable to assume that we have some control over the order that we interview them, and hence this is a natural extension. For example, a company, upon receiving resumes, can assign a predicted value to each candidate and interview them in the order proposed in this algorithm.
\section{Algorithm Motivation}
Consider the algorithm where we are able to arbitrarily fix the order of the candidates. Then, the worst-case robustness(where predictions are entirely random) is still bounded by $\frac{1}{e}$. We will proceed to show that allowing this restriction will improve the robustness of current the algorithm proposed previously of 0.215 (https://arxiv.org/pdf/2306.08340)



\section{Algorithm}
Our algorithm follows largely from the algorithm proposed by Fujii and Yoshida. 

Let $\hat{i} = \arg\max \left( \hat{v}(i) \right)$

then $T=t_{\hat{i}}$ will be defined as follows:
\[
T =
\begin{cases}
    \tau & \text{with probability } p_0 \\
    \tau + \epsilon &\text{with probability $p_1$ } \\
    1& \text{otherwise}
\end{cases}
\]

while the arrival timing of all other candidates are randomly uniform in $[0,1]$


\begin{algorithm}[H] % Using [H] to suggest placing it here, requires 'float' package or adjust as needed
\caption{Ordered Learned Dynkin}\label{alg:ordered_learned_dynkin}
\begin{algorithmic}[1] % The [1] enables line numbering
\REQUIRE Time $\tau$, Threshold $\theta$, Predictions $\hat{v} : \Ncal \to \Rbb$
    \STATE Internal Threshold $X \leftarrow 0$ % Use \leftarrow for assignment
    \STATE $\ihat \leftarrow \arg\max_{i \in \Ncal} \hat{v}(i)$ % Use \leftarrow and argmax operator
    \FORALL{$i \in \Ncal \setminus \{\ihat\}$}
        \STATE Let $t_i \sim \Udist(0,1)$ \COMMENT{Draw time/cost for candidate $i \neq \ihat$}
    \ENDFOR % Close the FORALL loop for time assignments

    \STATE Let $ t_{\ihat} \sim T$ % T is the distribution defined elsewhere

    \STATE $\text{mode} \leftarrow \text{Prediction}$ \COMMENT{Initialize mode}

    \FORALL{candidate $i \in \Ncal$ in order of arrival time $t_i$}
        \Statex % Add vertical space if desired
        % Check for mode switch condition
        \IF{$|1 - \frac{\hat{v}(i)}{v(i)}| > \theta$}  % Outer IF
            % --- Start of block executed if outer IF is true ---
            % Check specific condition to update threshold X
            \IF{$X=0$ and $\frac{\hat{v}(i)}{v(i)} - 1 > \theta$ \AND $i=\ihat$ \AND $\text{mode} = \text{Prediction}$} % Inner IF (use \AND)
                \IF{$t_i > \tau$ \AND $i$ is the best so far} % Use \AND, >=
                    \STATE Hire $i$
                    \STATE \RETURN
                \ENDIF
                 \STATE $X \leftarrow \max_{j\in \mathcal{N} \setminus \{\hat{i}\}} (1-\theta)\hat{v}(j))$ with probability $p_3$
            %\ELSIF{$i\neq \ihat$ and $X=0$ and $\text{mode} = \text{Prediction}$}
                %$X \leftarrow (1-\theta)\hat{v}(\ihat)$ with some probability $p_2$
            \ELSE % ELSE corresponding to Inner IF
                 \STATE $X \leftarrow 0$
            \ENDIF % End Inner IF
            % Always switch mode if outer IF was true
            \STATE $\text{mode} \leftarrow \text{Secretary}$
            % --- End of block executed if outer IF is true ---
        \ENDIF % End Outer IF
        \Statex

        % Check for hiring in Prediction mode
        \IF{$\text{mode} = \text{Prediction}$ \AND $i = \ihat$} % Use \AND
            \STATE Hire $i$
            \STATE \RETURN
        \ENDIF
        \Statex

        % Check for hiring in Secretary mode
        \IF{$\text{mode} = \text{Secretary}$ \AND $t_i > \tau$ \AND $i$ is the best so far \AND $v(i) \geq X$} % Use \AND, >=
            \STATE Hire $i$
            \STATE \RETURN
        \ENDIF

        % Check if last element (forced hire)
        \IF{$i$ is the last element in $\Ncal$}
            \STATE Hire $i$
            \STATE \RETURN
        \ENDIF
        \Statex
    \ENDFOR % End FORALL loop processing candidates
    % \State No candidate hired. (Optional: Add if algorithm can finish without hiring)
\end{algorithmic}
\end{algorithm}s
\section{T must not be constant in this algorithm for it to be competitive}
\subsection{$P(T>\tau)>0$}
Suppose otherwise, T is a constant s.t. $T<\tau$

Then, Case 5.1 will give a competitive ratio of 0 because $i*$ cannot be selected.
\subsection{$P(T<\tau)>0$}
Suppose otherwise, T is a constant s.t. $T>\tau$

Then, Case 5.4 will give a competitive ratio of 0 when $M=\{\hat{i}\}$ and $\hat{i}$ is the second largest element besides $i^*$

If $T<t_{i^*}$, then $\hat{i}$ will be selected at T. 

If $T>t_{i^*}$, then the algorithm will still be in prediction mode at $t_{i^*}$

Hence T must be a random variable.

\section{Cases}
\subsection{In the case where $\hat{i}=i^* \in M$}
\subsubsection{$T>\tau$}
Since $\hat{\imath} \in M$, the algorithm never hires a candidate when the mode is \textit{Prediction}. Hence, it is sufficient to consider the case where the algorithm hires a candidate when the mode is \textit{Secretary}. 

When we fix $T \in [\tau, 1]$, the algorithm hires $i^*$ if the best candidate in $[0, t^*)$ appears before $\tau$ (this is a sufficient but not a necessary condition). If we fix the set of candidates before $t^*$, the best candidate before $t^*$ is also fixed. Since the time when this candidate appears conforms to the uniform distribution on $[0, t^*)$, the probability that this time is earlier than $\tau$ is $\tau / t^*$. 

By taking the expectation over $t^*$, we can show that the success probability is
\[
\boxed{(1-p_0-p1)\tau+p_1}
\]



\subsection{In the case where $\hat{\imath} = i^* \notin M$.}
\subsubsection{$T>\tau$}
The algorithm hires $\hat{\imath}$ in the Prediction mode when $\hat{\imath}$ appears before all the candidates in $M$. Since $M \cup \{\hat{\imath}\}$ appear in random order, this probability is $A=1-(1-T)^m$, where $m = |M|$. 

Next, we consider the probability that the algorithm hires $i^*$ in the Secretary mode. It is $B=(1-T)^m\frac{\tau}{T}$

$\boxed{(1-p_0-p1)\tau+p_1}$

\subsection{In the case where $\hat{\imath} \neq i^*$, $\hat{\imath} \in M$, and $i^* \in M$.}
Since $\hat{\imath} \in M$, the algorithm switches to the Secretary mode when observing $\hat{\imath}$. The algorithm hires $i^*$ if $t^* \in [\tau, 1]$ and any candidate before $i^*$ is not hired. Hence, a sufficient condition for hiring $i^*$ is that $t^* \in [\tau, 1]$ and the best candidate in $[0, t^*)$ appears before $\tau$. Note that the algorithm might successfully hire $i^*$ if the algorithm observes the best candidate in $[0, t^*)$ after $\tau$ in the Prediction mode, but we ignore this probability. The probability that the sufficient condition holds can be computed in the same way as (i) as $\int_{\tau}^{1} \frac{\tau}{t^*} dt^* = \tau \ln \frac{1}{\tau}$.

If $T<=\tau$ answer is the
same as before, contributing
\[
p_0*(\tau \ln \frac{1}{\tau})
\]
If $T>\tau$ and $T>t_{i^*}$
contributing
\[ (1-p_0-p_1)*\int_\tau^1(1-(1-t)^{m-2})\frac{\tau}{t}+(1-t)^{m-2} dt \] 
Since there is a $(1-(1-t)^{m-2}$ chance that an element in M appears before $i^*$, and $((1-t)^{m-2}$ that $i^*$ is the first in all elements in M and hence will be chosen as long as the internal threshold is not set. 

the
total probability is 
\[
\boxed{p_0*(\tau \ln \frac{1}{\tau})+(1-p_0-p_1)*\int_\tau^1(1-(1-t)^{m-2})(\frac{\tau}{t})+(1-t)^{m-2}dt}
\]

\subsection{In the case where $\hat{i} \neq i^*, \hat{i} \in M, i^* \notin M$.}
%Consider the cases where i* is before T or after T

%Case 1: Before T:
%\[
%\int_\tau^T (1 - (1 - t^*)^{m-1}) \frac{\tau}{ t^*} dt^*
%\]
%Case 2: After T:
%\[
%\int_T^1 \frac{\tau}{ t^*} dt^*
%\]
%0 if $\hat{i}$ is the second largest element!
\subsubsection{$T<=\tau$}
Worst case is when m=1. When m=1, it means that aside from $\hat{i}$, all predictions are close from each element's true value. Hence, in secretary mode, either the algorithm picks the best element $i^*$ with $\frac{\tau}{t^*}$ chance or it picks a good enough element that is at most $\frac{1-\theta}{1+\theta}$otherwise for m=1
\subsubsubsection{$m=1$}
\[\boxed{
\phi>=p_0*\int_\tau^1\frac{\tau}{t^*}+p_3*\frac{t^*-\tau}{t^*}\frac{1-\theta}{1+\theta}dt^*}
\]
\subsubsubsection{$m>1$}
\[\boxed{
\phi>=p_0*\int_\tau^1 (p_3*((1-(1-t^*)^{m-1})\frac{\tau}{t^*})+((1-p_3)\frac{\tau}{t^*})dt^*}
\]
\subsubsection{$T>\tau$}
\subsubsubsection{$m=1$}
\[
0
\]
\subsubsubsection{$m>1$}
\[
\phi>=(1-p_0-p_1)\int_\tau^1((1-(1-t^*)^{m-1})\frac{\tau}{t^*}dt^*
\]

Hence, total competitive ratio for this case is min of 
$\phi_{m=1}$ and $\phi_{m>1}$
\[
\phi_{m=1}=p_0*\int_\tau^1\frac{\tau}{t^*}+p_3*\frac{t^*-\tau}{t^*}\frac{1-\theta}{1+\theta}dt^*
\]
\[
\phi_{m>1}=p_0*\int_\tau^1 (p_3*((1-(1-t^*)^{m-1})\frac{\tau}{t^*})+((1-p_3)\frac{\tau}{t^*})dt^* + (1-p_0-p_1)\int_\tau^1((1-(1-t^*)^{m-1})\frac{\tau}{t^*}dt^*
\]
\subsection{In the case where $\hat{i} \neq i^*$, $\hat{i} \notin M$, and $i^* \in M$}
\subsubsection{Case 1: $T=1$}
General case of m:
the only way for i* to be selected is if i* is the first element to appear in M, OR it if the max before him is before $\tau$
\[
\phi=\int_\tau^1 (1 - t^*)^{m-1})dt^* + \int_\tau^1(1-(1 - t^*)^{m-1})\frac{\tau}{ t^*} dt^*
\]

\subsubsection{Case 2: $T=\tau$}
In this case, we need an element in M to come before T. 
\[
\phi=\int_\tau^1(1-(1 - T)^{m-1})\frac{\tau}{ t^*} dt^*
\]

\subsubsection{Overall bounds}
\[
\boxed{\phi=(1-p_0-p_1)(\int_\tau^1 (1 - t^*)^{m-1}dt^* + \int_\tau^1(1-(1 - t^*)^{m-1})\frac{\tau}{ t^*} dt^*)+p_0(\int_\tau^1(1-(1 - \tau)^{m-1})\frac{\tau}{ t^*} dt^*)}
\]


\subsection{In the case where $\hat{i} \neq i^*$, $\hat{i} \notin M$, and $i^* \notin M$.}
\subsubsection{$T>\tau$} at t*, there is a $1-(1-t)^m$ chance that $t_m<t*$ and hence $t^*$ is accepted if the best element, $i_b$ is such that $t_{i_b}<\tau$. Otherwise, having missed $t^*$, it will select $\hat{i}$ instead as it will be forced to select the last element.
\[
\phi=\int_\tau^1(1-(1-t)^m)\frac{\tau}{t}+(1-t)^m\frac{1-\theta}{1+\theta} dt
\]
\subsubsection{$T=\tau$}
\subsubsubsection{For $t_m<T<t^*$}
\[
\phi=\int_\tau^1\int_0^\tau m(1-t_m)^{m-1}\frac{\tau-t_m}{t^*-t_m}dt_mdt^*
\]
\subsubsubsection{For $T<t_m$}
\[
(1-\tau)^m\frac{1-\theta}{1+\theta}
\]
\subsubsection{For $T=\tau+\epsilon$}
Suppose we force the algorithm to hire ihat. then we will get $\frac{1-\theta}{1+\theta}$

\subsubsection{Overall bounds}
\[
\phi=p_0(\int_\tau^1\int_0^\tau m(1-t_m)^{m-1}\frac{\tau-t_m}{t^*-t_m}dt_mdt^*+(1-\tau)^m\frac{1-\theta}{1+\theta}) + p1*(\frac{1-\theta}{1+\theta}) +(1-p_0-p_1)*(\int_\tau^1(1-(1-t)^m)\frac{\tau}{t}+(1-t)^m\frac{1-\theta}{1+\theta})
\]



\section{Distribution of T}


Let $T$ be defined as:
\[
T =
\begin{cases}
    \tau & \text{with probability } p_0 \\
    \tau + \epsilon &\text{with probability $p_1$ } \\
    1& \text{otherwise}
\end{cases}
\]


\subsection{Other directions}





1. Region of smoothness until error is so big that it just becomes robustness? (Tradeoff between smoothness and robustness)

\end{document}
