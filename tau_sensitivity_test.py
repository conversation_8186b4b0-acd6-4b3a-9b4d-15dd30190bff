import numpy as np
from main import secretary_problem
from learned_dynkin import learned_dynkin_algorithm
from robustness_test import classic_secretary_algorithm, Algorithm

class TauSensitivityTester:
    """
    A class to test algorithm performance across a range of tau values.
    """
    def __init__(self, algorithms, num_simulations=500, num_candidates=100):
        self.algorithms = algorithms
        self.num_simulations = num_simulations
        self.num_candidates = num_candidates
        self.results = {}

    def run_test(self, tau_values, noise_std_dev=0.1):
        """
        Runs simulations for each algorithm across a range of tau values.

        Args:
            tau_values (list of float): The tau values to test.
            noise_std_dev (float): The noise level for predictions.
        """
        print(f"\n--- Testing Sensitivity to Tau (Prediction Noise = {noise_std_dev}) ---")
        print(f"{'Tau Value':<15} | " + " | ".join([f"{alg.name:<25}" for alg in self.algorithms]))
        print("-" * (18 + len(self.algorithms) * 28))

        for tau in tau_values:
            # Run a full simulation for the current tau value
            self._run_simulation_for_tau(tau, noise_std_dev)
            
            # Report results for the current tau value
            performance_scores = [np.mean(self.results[alg.name]['hired_values']) for alg in self.algorithms]
            print(f"{tau:<15.2f} | " + " | ".join([f"{score:<25.4f}" for score in performance_scores]))

    def _run_simulation_for_tau(self, tau, noise_std_dev):
        """Helper function to run simulations for a single tau value."""
        for alg in self.algorithms:
            self.results[alg.name] = {'hired_values': []}

        for _ in range(self.num_simulations):
            candidates = list(range(self.num_candidates))
            v_actual = {i: np.random.rand() for i in candidates}
            v_predictions = {i: v_actual[i] + np.random.normal(0, noise_std_dev) for i in v_actual}

            for alg in self.algorithms:
                # Override the default tau for this specific test run
                hired_candidate = alg.run(v_predictions, v_actual, tau=tau)
                self.results[alg.name]['hired_values'].append(v_actual[hired_candidate])

if __name__ == '__main__':
    # Define the algorithms to be tested
    alg1 = Algorithm("Ordered Learned Dynkin", secretary_problem)
    alg2 = Algorithm("Learned Dynkin", learned_dynkin_algorithm)
    alg3 = Algorithm("Classic Secretary", classic_secretary_algorithm)

    # Initialize and run the tester
    tester = TauSensitivityTester([alg1, alg2, alg3])
    
    # Define the tau values to test
    tau_values_to_test = np.arange(0.1, 1.0, 0.1)
    tester.run_test(tau_values_to_test)
