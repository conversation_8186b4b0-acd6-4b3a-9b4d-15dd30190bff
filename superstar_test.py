import numpy as np
from main import secretary_problem
from learned_dynkin import learned_dynkin_algorithm
from robustness_test import classic_secretary_algorithm, Algorithm

class SuperstarBenchmark:
    """
    A class to benchmark algorithms in a 'superstar' scenario, where one
    candidate is significantly more valuable than the rest.
    """
    def __init__(self, algorithms, num_simulations=1000, num_candidates=100):
        self.algorithms = algorithms
        self.num_simulations = num_simulations
        self.num_candidates = num_candidates
        self.results = {}

    def _generate_superstar_values(self):
        """Generates values where one candidate is a 'superstar'."""
        candidates = list(range(self.num_candidates))
        # Most candidates have low value
        v_actual = {i: np.random.uniform(0, 0.1) for i in candidates}
        # One random candidate is the superstar
        superstar_id = np.random.choice(candidates)
        v_actual[superstar_id] = 1.0
        return v_actual, superstar_id

    def run_benchmark(self, prediction_quality='accurate'):
        """
        Runs the benchmark simulation.
        
        Args:
            prediction_quality (str): 'accurate' or 'random'.
        """
        print(f"\n--- Benchmarking: Superstar Scenario ({prediction_quality.capitalize()} Predictions) ---")
        
        for alg in self.algorithms:
            self.results[alg.name] = {'successful_hires': 0}

        for _ in range(self.num_simulations):
            v_actual, superstar_id = self._generate_superstar_values()

            # Generate predictions based on the desired quality
            if prediction_quality == 'accurate':
                # Predictions are noisy but point to the right superstar
                v_predictions = {i: v_actual[i] + np.random.normal(0, 0.05) for i in v_actual}
            else: # random
                v_predictions = {i: np.random.rand() for i in v_actual}

            for alg in self.algorithms:
                # Forcing fallback strategy for random predictions
                params = {'tau': np.e**-1, 'theta': 0} if prediction_quality == 'random' else {}
                hired_candidate = alg.run(v_predictions, v_actual, **params)
                if hired_candidate == superstar_id:
                    self.results[alg.name]['successful_hires'] += 1
        
        self._report_results()

    def _report_results(self):
        """Prints a formatted report of the benchmark results."""
        print(f"{'Algorithm':<25} {'Superstar Hire Rate':<25}")
        print("-" * 50)
        for name, data in self.results.items():
            success_rate = (data['successful_hires'] / self.num_simulations) * 100
            print(f"{name:<25} {success_rate:<25.2f}%")
        print("-" * 50)

if __name__ == '__main__':
    # Define the algorithms to be tested
    alg1 = Algorithm("Ordered Learned Dynkin", secretary_problem)
    alg2 = Algorithm("Learned Dynkin", learned_dynkin_algorithm)
    alg3 = Algorithm("Classic Secretary", classic_secretary_algorithm)

    # Initialize and run the benchmark
    benchmark = SuperstarBenchmark([alg1, alg2, alg3])
    benchmark.run_benchmark(prediction_quality='accurate')
    benchmark.run_benchmark(prediction_quality='random')
