import numpy as np
from main import secretary_problem
from learned_dynkin import learned_dynkin_algorithm
import random
def classic_secretary_algorithm(v_hat, v_actual, tau=np.e**-1, **kwargs):
    """
    Implementation of the classic secretary algorithm. Ignores v_hat.
    The 'tau' parameter represents the fraction of candidates to observe.
    """
    n_cal = list(v_actual.keys())
    
    # Handle edge cases for 0 or 1 candidates
    if not n_cal:
        return -1, {} # Return a dummy value if no candidates
    if len(n_cal) == 1:
        return n_cal[0], {n_cal[0]: 0}

    # Process candidates in random order
    arrival_times = {i: np.random.rand() for i in n_cal}
    sorted_by_arrival = sorted(n_cal, key=lambda i: arrival_times[i])

    # Determine the observation group, ensuring it's not empty
    num_to_observe = int(len(n_cal) * tau)
    if num_to_observe == 0:
        num_to_observe = 1 # Always observe at least one if there are multiple candidates

    observation_group = sorted_by_arrival[:num_to_observe]
    best_in_observation = max(v_actual[i] for i in observation_group)

    # Start the selection phase
    selection_group = sorted_by_arrival[num_to_observe:]
    for i in selection_group:
        if v_actual[i] > best_in_observation:
            return i, arrival_times

    # If no one better is found, hire the last one.
    return sorted_by_arrival[-1], arrival_times

class Algorithm:
    """A wrapper class for secretary algorithms to standardize their interface."""
    def __init__(self, name, function):
        self.name = name
        self.function = function

    def run(self, v_hat, v_actual, **kwargs):
        # The function is called with essential arguments plus any custom test parameters.
        hired, _ = self.function(v_hat, v_actual, **kwargs )
        return hired, _

class RobustnessTester:
    """A class to test the robustness of algorithms against bad predictions."""
    def __init__(self, algorithms, num_simulations=1000, num_candidates=100):
        self.algorithms = algorithms
        self.num_simulations = num_simulations
        self.num_candidates = num_candidates
        self.results = {}

    def _run_simulation(self, prediction_generator, **kwargs):
        """Helper function to run a single set of simulations with custom parameters."""
        for alg in self.algorithms:
            self.results[alg.name] = {'hired_values': []}

        for _ in range(self.num_simulations):
            candidates = list(range(self.num_candidates))
            # Use a power-law distribution to create large differences in values
            v_actual = {i: np.random.rand()**10 for i in candidates}
            
            # Generate predictions using the provided strategy
            v_predictions = prediction_generator(v_actual)

            for alg in self.algorithms:
                hired_candidate = alg.run(v_predictions, v_actual, **kwargs)
                self.results[alg.name]['hired_values'].append(v_actual[hired_candidate])

    def test_inverse_predictions(self):
        """Tests algorithms when predictions are inversely correlated with actual values."""
        print("\n--- Testing Robustness: Inverse Predictions ---")
        print(f"Forcing fallback to Secretary mode with tau=1/e ({np.e**-1:.4f}) and theta=0.")
        
        def inverse_generator(v_actual):
            return {i: 1.0 - v_actual[i] for i in v_actual}

        self._run_simulation(inverse_generator, tau=np.e**-1, theta=0)
        self._report_results()

    def test_random_predictions(self):
        """Tests algorithms when predictions are completely random."""
        print("\n--- Testing Robustness: Random Predictions ---")
        print(f"Forcing fallback to Secretary mode with tau=1/e ({np.e**-1:.4f}) and theta=0.")

        def random_generator(v_actual):
            return {i: np.random.rand() for i in v_actual}

        self._run_simulation(random_generator, tau=np.e**-1, theta=0)
        self._report_results()

    def _report_results(self):
        """Prints a formatted report of the latest simulation results."""
        print(f"{'Algorithm':<25} {'Avg. Hired Value':<25}")
        print("-" * 50)
        for name, data in self.results.items():
            avg_value = np.mean(data['hired_values'])
            print(f"{name:<25} {avg_value:<25.4f}")
        print("-" * 50)

if __name__ == '__main__':
    # Define the algorithms to be tested
    alg1 = Algorithm("Ordered Learned Dynkin", secretary_problem)
    alg2 = Algorithm("Learned Dynkin", learned_dynkin_algorithm)
    alg3 = Algorithm("Classic Secretary", classic_secretary_algorithm)

    # Initialize and run the tester
    tester = RobustnessTester([alg1, alg2, alg3])
    tester.test_inverse_predictions()
    tester.test_random_predictions()
