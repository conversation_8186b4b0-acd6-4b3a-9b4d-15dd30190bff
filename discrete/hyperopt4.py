import numpy as np
from scipy.integrate import quad, dblquad # Import dblquad
from scipy.optimize import minimize
import math
import warnings

# Fixed parameter
THETA = 0.631
CONST_FACTOR = (1 - THETA) / (1 + THETA) # 0.25

# m ranges
M_RANGE_FULL = range(1, 21) # m = 1 to 20
M_RANGE_CASE3 = range(2, 21) # m = 2 to 20
M_RANGE_CASE4_GT1 = range(2, 21) # m = 2 to 20

# --- Define Functions for Each Case ---

# Cases 5.1 and 5.2 (Unchanged)
def calculate_case1_2(tau, p0, p1, p3):
    prob_T_1 = max(0, 1 - p0 - p1)
    # Using the BOXED formula provided: (1-p0-p1)*tau + p1
    return prob_T_1 * tau + p1

# Case 5.3 (Unchanged)
def calculate_case3(tau, p0, p1, p3, m):
    if m < 2:
        return np.inf
    prob_T_tau = p0
    prob_T_1 = max(0, 1 - p0 - p1)
    term_tau = 0
    if prob_T_tau > 1e-9 and tau > 1e-9 and tau < 1.0 - 1e-9:
         term_tau = prob_T_tau * tau * math.log(1.0 / tau)
    term_1 = 0
    if prob_T_1 > 1e-9 and tau < 1.0 - 1e-9:
        term_1_integrand = lambda t: (1 - (1 - t)**(m - 2)) * (tau / t) + (1 - t)**(m - 2)
        try:
            integral_1, err_1 = quad(term_1_integrand, tau, 1.0, epsabs=1e-6, epsrel=1e-6) # Adjusted tolerance
            if err_1 > 1e-3:
                 warnings.warn(f"High integration error ({err_1:.2e}) in Case 3 for m={m}, tau={tau:.3f}")
            term_1 = prob_T_1 * integral_1
        except Exception as e:
            warnings.warn(f"Integration failed in Case 3 for m={m}, tau={tau:.3f}: {e}")
            return -np.inf # Indicate failure
    return term_tau + term_1

# Case 5.4 (Unchanged, added tolerance adjustment)
def calculate_case4(tau, p0, p1, p3, m):
    prob_T_tau = p0
    prob_T_1 = max(0, 1 - p0 - p1)
    result = 0

    # --- m = 1 subcase ---
    if m == 1:
        if prob_T_tau > 1e-9 and tau < 1.0 - 1e-9:
            def integrand_m1(t):
                if abs(t) < 1e-9: return 0
                return tau / t + p3 * (1 - tau / t) * CONST_FACTOR
            try:
                integral_m1, err_m1 = quad(integrand_m1, tau, 1.0, epsabs=1e-6, epsrel=1e-6)
                if err_m1 > 1e-3:
                     warnings.warn(f"High integration error ({err_m1:.2e}) in Case 4 (m=1), tau={tau:.3f}")
                result = prob_T_tau * integral_m1
            except Exception as e:
                 warnings.warn(f"Integration failed in Case 4 (m=1), tau={tau:.3f}: {e}")
                 return -np.inf # Indicate failure
        # Note: The formula provided only had a T=tau term for m=1. If T=1 should contribute, it's missing.
        return result

    # --- m > 1 subcase ---
    else:
        term_tau = 0
        if prob_T_tau > 1e-9 and tau < 1.0 - 1e-9:
            def integrand_tau_m_gt1(t):
                 if abs(t) < 1e-9: return 0
                 return (p3 * (1 - (1 - t)**(m - 1)) + (1 - p3)) * (tau / t)
            try:
                integral_tau, err_tau = quad(integrand_tau_m_gt1, tau, 1.0, epsabs=1e-6, epsrel=1e-6)
                if err_tau > 1e-3:
                     warnings.warn(f"High integration error ({err_tau:.2e}) in Case 4 (m>1, T=tau) for m={m}, tau={tau:.3f}")
                term_tau = prob_T_tau * integral_tau
            except Exception as e:
                 warnings.warn(f"Integration failed in Case 4 (m>1, T=tau) for m={m}, tau={tau:.3f}: {e}")
                 return -np.inf # Indicate failure

        term_1 = 0
        if prob_T_1 > 1e-9 and tau < 1.0 - 1e-9:
            def integrand_1_m_gt1(t):
                if abs(t) < 1e-9: return 0
                return (1 - (1 - t)**(m - 1)) * (tau / t)
            try:
                integral_1, err_1 = quad(integrand_1_m_gt1, tau, 1.0, epsabs=1e-6, epsrel=1e-6)
                if err_1 > 1e-3:
                     warnings.warn(f"High integration error ({err_1:.2e}) in Case 4 (m>1, T=1) for m={m}, tau={tau:.3f}")
                term_1 = prob_T_1 * integral_1
            except Exception as e:
                 warnings.warn(f"Integration failed in Case 4 (m>1, T=1) for m={m}, tau={tau:.3f}: {e}")
                 return -np.inf # Indicate failure

        return term_tau + term_1

# Case 5.5 (Unchanged, added tolerance adjustment)
def calculate_case5(tau, p0, p1, p3, m):
    prob_T_tau = p0
    prob_T_1 = max(0, 1 - p0 - p1)

    term_1 = 0
    if prob_T_1 > 1e-9 and tau < 1.0 - 1e-9:
        integral_1_val = 0
        if m > 0:
             integral_1_val = (1 - tau)**m / m

        integral_1_part2_val = 0
        def integrand_1_part2(t):
            if abs(t) < 1e-9: return 0
            term_pow = (1.0 - t)**(m - 1) if m > 0 else 0 # Handle m=0 just in case, though range is 1-20
            if m == 1: term_pow = 1.0 # (1-t)^0 = 1
            return (1.0 - term_pow) * (tau / t)

        try:
            integral_1_part2_val, err_1_p2 = quad(integrand_1_part2, tau, 1.0, epsabs=1e-6, epsrel=1e-6)
            if err_1_p2 > 1e-3:
                 warnings.warn(f"High integration error ({err_1_p2:.2e}) in Case 5 (T=1) for m={m}, tau={tau:.3f}")
        except Exception as e:
             warnings.warn(f"Integration failed in Case 5 (T=1) for m={m}, tau={tau:.3f}: {e}")
             return -np.inf # Indicate failure

        term_1 = prob_T_1 * (integral_1_val + integral_1_part2_val)

    term_tau = 0
    if prob_T_tau > 1e-9 and tau > 1e-9 and tau < 1.0 - 1e-9:
        const_factor = 0.0
        if m > 1: # If m=1, (1-(1-tau)^0)*tau = 0
            const_factor = (1.0 - (1.0 - tau)**(m - 1)) * tau

        if abs(const_factor) > 1e-12:
             integral_tau_val = const_factor * math.log(1.0 / tau)
             term_tau = prob_T_tau * integral_tau_val

    return term_1 + term_tau


# --- Case 5.6: MODIFIED to include Double Integral ---

# Define the integrand for the double integral part of Case 5.6
def case6_double_integrand(tm, t_star, m, tau):
    """
    Integrand for the double integral in Case 5.6 (T=tau part).
    ∫[τ,1] ∫[0,τ] m * (1-tm)**(m-1) * (τ-tm) / (t*-tm) dtm dt*
    Args:
        tm (float): Inner variable of integration.
        t_star (float): Outer variable of integration.
        m (int): Parameter m.
        tau (float): Parameter tau.
    Returns:
        float: Value of the integrand.
    """
    # Check for the singularity t_star = tm.
    # This should only occur at the boundary point t_star = tm = tau.
    # The numerator (tau - tm) is also 0 at this point.
    # dblquad can often handle integrable singularities at boundaries.
    # If issues arise, returning 0.0 here might be a pragmatic approach.
    if abs(t_star - tm) < 1e-12:
        # print(f"DEBUG: Hit singularity t_star={t_star}, tm={tm}")
        return 0.0 # Avoid division by zero; numerator is also zero

    # Ensure tm is within its integration bounds [0, tau] for safety
    # Although dblquad should handle this via limits.
    if tm < -1e-9 or tm > tau + 1e-9:
         # print(f"DEBUG: tm={tm} out of bounds [0, {tau}]")
         return 0.0

    # Calculate (1-tm)**(m-1) carefully
    if m == 1:
        pow_term = 1.0 # (1-tm)^0 = 1
    else:
        base = 1.0 - tm
        # Ensure base is non-negative, though it should be since tm <= tau <= 1
        pow_term = (max(0.0, base))**(m - 1)

    numerator = m * pow_term * (tau - tm)
    denominator = t_star - tm

    # Add checks for potential NaN/Inf before returning
    val = numerator / denominator
    if not np.isfinite(val):
        # print(f"DEBUG: Non-finite value: num={numerator}, den={denominator}, tm={tm}, t*={t_star}, m={m}, tau={tau}")
        return 0.0 # Or handle appropriately
    return val


def calculate_case6(tau, p0, p1, p3, m):
    prob_T_tau = p0
    prob_T_eps = p1
    prob_T_1 = max(0, 1 - p0 - p1)

    # --- Term for T = tau ---
    # phi = p0 * ( DOUBLE_INTEGRAL + (1-tau)**m * CONST_FACTOR )
    term_tau = 0.0
    if prob_T_tau > 1e-9 and tau > 1e-9 and tau < 1.0 - 1e-9: # Need tau > 0 for inner limit and tau < 1 for outer limit
        # Calculate the double integral part
        double_integral_val = 0.0
        try:
            # Note: Increased tolerances for dblquad due to potential complexity/speed
            integral_val, integral_err = dblquad(
                case6_double_integrand,
                tau, 1.0,              # Outer limits (t_star) a to b
                lambda t_star: 0.0,     # Inner lower limit (tm) g(t_star)
                lambda t_star: tau,     # Inner upper limit (tm) h(t_star)
                args=(m, tau),
                epsabs=1e-5, # Looser tolerance for performance
                epsrel=1e-5
            )
            if integral_err > 5e-3: # Slightly higher warning threshold for dblquad
                 warnings.warn(f"High double integration error ({integral_err:.2e}) in Case 6 (T=tau) for m={m}, tau={tau:.3f}")
            double_integral_val = integral_val

        except Exception as e:
            # Catch potential errors during integration (e.g., convergence)
            warnings.warn(f"Double integration failed in Case 6 (T=tau) for m={m}, tau={tau:.3f}: {e}")
            # If double integral fails, we cannot reliably compute this case value
            return -np.inf # Return a value indicating failure

        # Calculate the second part: (1 - tau)**m * CONST_FACTOR
        original_term = (1.0 - tau)**m * CONST_FACTOR

        # Combine terms for T=tau
        term_tau = prob_T_tau * (double_integral_val + original_term)


    # --- Term for T = tau + epsilon ---
    term_eps = prob_T_eps * CONST_FACTOR

    # --- Term for T = 1 ---
    # phi = (1-p0-p1) * ( integral[ (1-(1-t)**m)*(tau/t) + (1-t)**m * CONST_FACTOR ] dt )
    term_1 = 0.0
    if prob_T_1 > 1e-9 and tau < 1.0 - 1e-9:
        def integrand_1(t):
            if abs(t) < 1e-9: return 0
            pow_term = (1.0 - t)**m
            return (1.0 - pow_term) * (tau / t) + pow_term * CONST_FACTOR

        try:
            integral_1_val, err_1 = quad(integrand_1, tau, 1.0, epsabs=1e-6, epsrel=1e-6)
            if err_1 > 1e-3:
                 warnings.warn(f"High integration error ({err_1:.2e}) in Case 6 (T=1) for m={m}, tau={tau:.3f}")
            term_1 = prob_T_1 * integral_1_val
        except Exception as e:
             warnings.warn(f"Integration failed in Case 6 (T=1) for m={m}, tau={tau:.3f}: {e}")
             return -np.inf # Indicate failure

    # --- Total for Case 6 ---
    total = term_tau + term_eps + term_1
    if not np.isfinite(total):
        warnings.warn(f"Non-finite result in calculate_case6 for m={m}, tau={tau:.3f}, p0={p0:.3f}, p1={p1:.3f}")
        return -np.inf # Indicate failure or invalid result
    return total


# --- Objective Function (Handles potential -inf from failed integrations) ---

def objective_function(params):
    """
    Calculates the minimum competitive ratio across all cases for the given parameters.
    We want to MAXIMIZE this minimum, so the optimizer will MINIMIZE its NEGATIVE.
    params: [tau, p0, p1, p3]
    """
    tau, p0, p1, p3 = params

    if not (0 <= tau <= 1 and 0 <= p0 <= 1 and 0 <= p1 <= 1 and 0 <= p3 <= 1):
        return np.inf
    if p0 + p1 > 1.00001:
        return np.inf

    tau = np.clip(tau, 1e-7, 1.0 - 1e-7) # Clip slightly more aggressively maybe

    results = []
    min_ratio = np.inf # Start assuming a high ratio

    # --- Calculate results for each case, checking for failures (-inf) ---

    # Case 1/2
    val_12 = calculate_case1_2(tau, p0, p1, p3)
    if not np.isfinite(val_12): return np.inf # Failed
    min_ratio = min(min_ratio, val_12)

    # Case 3 (min over m=2..20)
    case3_vals = [calculate_case3(tau, p0, p1, p3, m) for m in M_RANGE_CASE3]
    valid_case3_vals = [v for v in case3_vals if np.isfinite(v)]
    if not valid_case3_vals: return np.inf # All failed
    min_ratio = min(min_ratio, min(valid_case3_vals))

    # Case 4 (min over m=1..20)
    case4_m1 = calculate_case4(tau, p0, p1, p3, m=1)
    if not np.isfinite(case4_m1): return np.inf
    case4_m_gt1_vals = [calculate_case4(tau, p0, p1, p3, m) for m in M_RANGE_CASE4_GT1]
    valid_case4_m_gt1_vals = [v for v in case4_m_gt1_vals if np.isfinite(v)]
    min_case4_m_gt1 = min(valid_case4_m_gt1_vals) if valid_case4_m_gt1_vals else np.inf
    min_ratio = min(min_ratio, case4_m1, min_case4_m_gt1)

    # Case 5 (min over m=1..20)
    case5_vals = [calculate_case5(tau, p0, p1, p3, m) for m in M_RANGE_FULL]
    valid_case5_vals = [v for v in case5_vals if np.isfinite(v)]
    if not valid_case5_vals: return np.inf
    min_ratio = min(min_ratio, min(valid_case5_vals))

    # Case 6 (min over m=1..20) - Now includes double integral
    case6_vals = [calculate_case6(tau, p0, p1, p3, m) for m in M_RANGE_FULL]
    valid_case6_vals = [v for v in case6_vals if np.isfinite(v)]
    if not valid_case6_vals: return np.inf
    min_ratio = min(min_ratio, min(valid_case6_vals))

    # We want to maximize min_ratio, so minimize -min_ratio
    # Ensure min_ratio is not inf before negating
    if not np.isfinite(min_ratio):
        return np.inf # Should have been caught earlier, but safety check

    return -min_ratio

# --- Optimization ---

# Bounds for variables [tau, p0, p1, p3]
bounds = [(1e-6, 1.0 - 1e-6), # tau
          (0.0, 1.0),        # p0
          (0.0, 1.0),        # p1
          (0.0, 1.0)]        # p3

# Constraints: p0 + p1 <= 1
constraints = ({'type': 'ineq', 'fun': lambda p: 1.0 - p[1] - p[2]})

# Initial guess
initial_guess = [0.37, 0.4, 0.1, 0.5] # [tau, p0, p1, p3] - Keep the same starting point

print("Starting optimization (including double integral - may be slow)...")
print(f"Theta = {THETA}, Const Factor = {CONST_FACTOR:.4f}")
print(f"Initial Guess: {initial_guess}")
print(f"Bounds: {bounds}")
print(f"Constraints: p0 + p1 <= 1")
print("-" * 30)

# Suppress warnings during optimization (optional)
# warnings.filterwarnings("ignore")

# Using SLSQP method
result = minimize(objective_function,
                  initial_guess,
                  method='SLSQP',
                  bounds=bounds,
                  constraints=constraints,
                  options={'disp': True, 'maxiter': 2000, 'ftol': 1e-6}) # Reduced maxiter, slightly looser ftol due to dblquad cost

# Restore warnings
# warnings.filterwarnings("default")

print("-" * 30)

if result.success:
    optimal_params = result.x
    max_min_ratio = -result.fun # Negate back to get the maximum minimum ratio

    print("Optimization Successful!")
    print(f"Optimal tau: {optimal_params[0]:.6f}")
    print(f"Optimal p0:  {optimal_params[1]:.6f}")
    print(f"Optimal p1:  {optimal_params[2]:.6f}")
    print(f"Optimal p3:  {optimal_params[3]:.6f}")
    print(f"Prob T=1:    {max(0, 1 - optimal_params[1] - optimal_params[2]):.6f}")
    print(f"Maximum Minimum Competitive Ratio: {max_min_ratio:.6f}")

    # --- Recalculate the individual case values at the optimum ---
    print("-" * 30)
    print("Competitive ratios for each case at optimum:")
    tau_opt, p0_opt, p1_opt, p3_opt = optimal_params
    # Ensure clipping for recalculation, consistent with objective function
    tau_opt = np.clip(tau_opt, 1e-7, 1.0 - 1e-7)
    all_calculated_crs = [] # Store successfully calculated ratios

    # --- Case 1/2 ---
    try:
        cr12 = calculate_case1_2(tau_opt, p0_opt, p1_opt, p3_opt)
        if np.isfinite(cr12):
            print(f"Case 1/2: {cr12:.6f}")
            all_calculated_crs.append(cr12)
        else:
            print("Case 1/2: Calculation resulted in non-finite value at optimum.")
    except Exception as e:
        print(f"Case 1/2: Error during final check: {e}")

    # --- Case 3 ---
    try:
        case3_vals = [calculate_case3(tau_opt, p0_opt, p1_opt, p3_opt, m) for m in M_RANGE_CASE3]
        valid_case3_vals = [v for v in case3_vals if np.isfinite(v)]
        if valid_case3_vals:
            cr3 = min(valid_case3_vals)
            print(f"Case 3:   {cr3:.6f} (worst m in {M_RANGE_CASE3})")
            all_calculated_crs.append(cr3)
        else:
            print(f"Case 3:   Failed calculation or no valid m at optimum (m in {M_RANGE_CASE3})")
    except Exception as e:
        print(f"Case 3:   Error during final check: {e}")

    # --- Case 4 ---
    try:
        # Calculate for m=1
        val_m1 = calculate_case4(tau_opt, p0_opt, p1_opt, p3_opt, m=1)
        # Calculate for m > 1
        vals_gt1 = [calculate_case4(tau_opt, p0_opt, p1_opt, p3_opt, m) for m in M_RANGE_CASE4_GT1]
        # Combine and find minimum valid result
        all_case4_vals = [val_m1] + vals_gt1
        valid_case4_vals = [v for v in all_case4_vals if np.isfinite(v)]
        if valid_case4_vals:
            cr4 = min(valid_case4_vals)
            print(f"Case 4:   {cr4:.6f} (worst m in {M_RANGE_FULL})")
            all_calculated_crs.append(cr4)
        else:
             print(f"Case 4:   Failed calculation or no valid m at optimum (m in {M_RANGE_FULL})")
    except Exception as e:
        print(f"Case 4:   Error during final check: {e}")

    # --- Case 5 ---
    try:
        case5_vals = [calculate_case5(tau_opt, p0_opt, p1_opt, p3_opt, m) for m in M_RANGE_FULL]
        valid_case5_vals = [v for v in case5_vals if np.isfinite(v)]
        if valid_case5_vals:
            cr5 = min(valid_case5_vals)
            print(f"Case 5:   {cr5:.6f} (worst m in {M_RANGE_FULL})")
            all_calculated_crs.append(cr5)
        else:
            print(f"Case 5:   Failed calculation or no valid m at optimum (m in {M_RANGE_FULL})")
    except Exception as e:
        print(f"Case 5:   Error during final check: {e}")

    # --- Case 6 ---
    try:
        case6_vals = [calculate_case6(tau_opt, p0_opt, p1_opt, p3_opt, m) for m in M_RANGE_FULL]
        valid_case6_vals = [v for v in case6_vals if np.isfinite(v)]
        if valid_case6_vals:
            cr6 = min(valid_case6_vals)
            print(f"Case 6:   {cr6:.6f} (worst m in {M_RANGE_FULL})")
            all_calculated_crs.append(cr6)
        else:
            print(f"Case 6:   Failed calculation or no valid m at optimum (m in {M_RANGE_FULL})")
    except Exception as e:
        print(f"Case 6:   Error during final check: {e}")

    # --- Final Minimum Check ---
    if all_calculated_crs: # Check if we got any valid results
        final_min = min(all_calculated_crs)
        print(f"Min Check:{final_min:.6f} (should match max_min_ratio)")
    else:
        print("Min Check: Could not calculate minimum ratio from individual cases due to errors/non-finite results.")

else: # If optimization failed
    print("Optimization Failed!")
    print(f"Message: {result.message}")
    # Non-finite function value often indicates persistent integration errors
    print(f"Final function value (negative min ratio): {result.fun}")
    print(f"Final parameters: {result.x}")
    print("Failure might be due to numerical instability in integrations (especially dblquad).")
    print("Consider adjusting tolerances (epsabs/epsrel) or simplifying the model if issues persist.")