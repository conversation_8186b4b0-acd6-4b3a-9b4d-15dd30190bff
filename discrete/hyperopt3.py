import numpy as np
from scipy.integrate import quad, dblquad
from scipy.optimize import differential_evolution
import warnings
import math
import time

# --- Constants and Setup ---
THETA = 0.6
EPS = 1e-9
M_MIN_phi2 = 2
M_MIN_phi_other = 1
# *** NEW: Set M_MAX_INT to 20 ***
M_MAX_INT = 20
# *** Update M_VALUES lists based on M_MAX_INT = 20 ***
M_VALUES_phi2 = list(range(M_MIN_phi2, M_MAX_INT + 1)) # 2 to 20
M_VALUES_other = list(range(M_MIN_phi_other, M_MAX_INT + 1)) # 1 to 20

ERROR_PENALTY = -1e18
THETA_FACTOR = (1.0 - THETA) / (1.0 + THETA) if abs(1.0 + THETA) > EPS else 0

# --- Phi Function Definitions (Including CORRECTED phi6) ---

# --- phi1_new ---
def phi1_new(p_0, p_1, p_2, p_3, tau, O, m, theta):
    p_0_eff = np.clip(p_0, 0.0, 1.0)
    p_1_eff = np.clip(p_1, 0.0, 1.0)
    coeff = 1.0 - p_0_eff - p_1_eff
    if coeff < -EPS: return ERROR_PENALTY
    coeff = max(0.0, coeff)
    if not (EPS <= tau <= 1.0 - EPS): return ERROR_PENALTY
    term1 = coeff * tau
    val = term1 + p_1_eff
    return val if np.isfinite(val) else ERROR_PENALTY

# --- phi2_new ---
def phi2_new(p_0, p_1, p_2, p_3, tau, O, m, theta):
    if m < M_MIN_phi2: return ERROR_PENALTY
    p_0_eff = np.clip(p_0, 0.0, 1.0)
    p_1_eff = np.clip(p_1, 0.0, 1.0)
    p_2_eff = np.clip(p_2, 0.0, 1.0)
    coeff = 1.0 - p_0_eff - p_1_eff
    if coeff < -EPS: return ERROR_PENALTY
    coeff = max(0.0, coeff)
    if not (EPS <= tau <= 1.0 - EPS): return ERROR_PENALTY
    term1 = 0.0
    if p_0_eff > EPS:
        if tau < EPS: term1 = ERROR_PENALTY
        else:
            try:
                log_val = np.log(1.0 / max(tau, EPS))
                term1 = p_0_eff * tau * log_val
                if not np.isfinite(term1): term1 = ERROR_PENALTY
            except Exception: term1 = ERROR_PENALTY
    if term1 <= ERROR_PENALTY + 1: return ERROR_PENALTY
    term2 = 0.0
    if coeff > EPS:
        def integrand_phi2(t):
            t_safe = max(t, EPS)
            factor_pow = 0.0
            if t_safe >= 1.0 - EPS: factor_pow = 1.0 if m == 2 else 0.0
            else:
                base = 1.0 - t_safe
                try: factor_pow = base**(m - 2)
                except Exception: factor_pow = 0.0
                if not np.isfinite(factor_pow): factor_pow = 0.0
            term_a = (1.0 - factor_pow) * (tau / t_safe)
            term_b = factor_pow * (1.0 - p_2_eff)
            res = term_a + term_b
            return res if np.isfinite(res) else 0.0
        try:
            integral_val, integ_err = quad(integrand_phi2, tau, 1.0, epsabs=1e-6, epsrel=1e-6, limit=100)
            if not np.isfinite(integral_val) or abs(integ_err) > 1e-2: integral_val = ERROR_PENALTY
            if integral_val <= ERROR_PENALTY + 1: term2 = ERROR_PENALTY
            else: term2 = coeff * integral_val
        except Exception as e: term2 = ERROR_PENALTY
    if term2 <= ERROR_PENALTY + 1: return ERROR_PENALTY
    result = term1 + term2
    return result if np.isfinite(result) else ERROR_PENALTY

# --- phi3_new ---
def phi3_new(p_0, p_1, p_2, p_3, tau, O, m, theta):
    p_0_eff = np.clip(p_0, 0.0, 1.0)
    p_3_eff = np.clip(p_3, 0.0, 1.0)
    if not (EPS <= tau <= 1.0 - EPS): return ERROR_PENALTY
    term1 = 0.0
    if p_0_eff > EPS:
        if tau < EPS or tau >= 1.0 - EPS: term1 = 0.0
        else:
            try:
                integral1_val = tau * np.log(1.0 / tau)
                if not np.isfinite(integral1_val): integral1_val = ERROR_PENALTY
                term1 = p_0_eff * integral1_val
            except Exception: term1 = ERROR_PENALTY
    if term1 <= ERROR_PENALTY + 1: return ERROR_PENALTY
    term2 = 0.0
    if p_3_eff > EPS:
        def integrand_phi3_term2(t_star):
            t_star_safe = max(t_star, EPS)
            val = (1.0 - tau / t_star_safe) * THETA_FACTOR
            return val if np.isfinite(val) else 0.0
        try:
            integral2_val, integ_err = quad(integrand_phi3_term2, tau, 1.0, epsabs=1e-6, epsrel=1e-6, limit=100)
            if not np.isfinite(integral2_val) or abs(integ_err) > 1e-2: integral2_val = ERROR_PENALTY
            term2 = p_3_eff * integral2_val
        except Exception: term2 = ERROR_PENALTY
    if term2 <= ERROR_PENALTY + 1: return ERROR_PENALTY
    result = term1 + term2
    return result if np.isfinite(result) else ERROR_PENALTY

# --- phi4_new ---
def phi4_new(p_0, p_1, p_2, p_3, tau, O, m, theta):
    if m < M_MIN_phi_other: return ERROR_PENALTY
    p_0_eff = np.clip(p_0, 0.0, 1.0)
    p_1_eff = np.clip(p_1, 0.0, 1.0)
    p_2_eff = np.clip(p_2, 0.0, 1.0)
    p_3_eff = np.clip(p_3, 0.0, 1.0)
    coeff2 = 1.0 - p_0_eff - p_1_eff
    if coeff2 < -EPS: return ERROR_PENALTY
    coeff2 = max(0.0, coeff2)
    if not (EPS <= tau <= 1.0 - EPS): return ERROR_PENALTY
    def common_factor(t_star, m_val):
         if t_star >= 1.0 - EPS: return 1.0
         if t_star < EPS: t_star = EPS
         base = 1.0 - t_star
         try:
             pow_val = base**(m_val - 1)
             if not np.isfinite(pow_val): pow_val = 0.0
             return 1.0 - pow_val
         except Exception: return 1.0
    term1 = 0.0
    if p_0_eff > EPS:
        def integrand_phi4_term1(t_star):
            t_star_safe = max(t_star, EPS)
            factor = common_factor(t_star_safe, m)
            val = (p_3_eff * factor + (1.0 - p_3_eff)) * (tau / t_star_safe)
            return val if np.isfinite(val) else 0.0
        try:
            integral1_val, integ_err = quad(integrand_phi4_term1, tau, 1.0, epsabs=1e-6, epsrel=1e-6, limit=100)
            if not np.isfinite(integral1_val) or abs(integ_err) > 1e-2: integral1_val = ERROR_PENALTY
            term1 = p_0_eff * integral1_val
        except Exception: term1 = ERROR_PENALTY
    if term1 <= ERROR_PENALTY + 1: return ERROR_PENALTY
    term2 = 0.0
    coeff_term2 = coeff2 * (1.0 - p_2_eff)
    if coeff_term2 > EPS:
        def integrand_phi4_term2(t_star):
            t_star_safe = max(t_star, EPS)
            factor = common_factor(t_star_safe, m)
            val = factor * (tau / t_star_safe)
            return val if np.isfinite(val) else 0.0
        try:
            integral2_val, integ_err = quad(integrand_phi4_term2, tau, 1.0, epsabs=1e-6, epsrel=1e-6, limit=100)
            if not np.isfinite(integral2_val) or abs(integ_err) > 1e-2: integral2_val = ERROR_PENALTY
            term2 = coeff_term2 * integral2_val
        except Exception: term2 = ERROR_PENALTY
    if term2 <= ERROR_PENALTY + 1: return ERROR_PENALTY
    result = term1 + term2
    return result if np.isfinite(result) else ERROR_PENALTY

# --- phi5_new ---
def phi5_new(p_0, p_1, p_2, p_3, tau, O, m, theta):
    if m < M_MIN_phi_other: return ERROR_PENALTY
    p_0_eff = np.clip(p_0, 0.0, 1.0)
    p_1_eff = np.clip(p_1, 0.0, 1.0)
    p_2_eff = np.clip(p_2, 0.0, 1.0)
    coeff1 = 1.0 - p_0_eff - p_1_eff
    if coeff1 < -EPS: return ERROR_PENALTY
    coeff1 = max(0.0, coeff1)
    if not (EPS <= tau <= 1.0 - EPS): return ERROR_PENALTY
    term1 = 0.0
    if coeff1 > EPS:
        integralA = 0.0
        coeffA = 1.0 - p_2_eff
        if coeffA > EPS:
            def integrand_phi5_A(t_star):
                if t_star >= 1.0 - EPS: return 0.0
                base = 1.0 - t_star
                try: pow_val = base**(m - 1)
                except Exception: pow_val = 0.0
                return pow_val if np.isfinite(pow_val) else 0.0
            try:
                integralA_val, integ_err = quad(integrand_phi5_A, tau, 1.0, epsabs=1e-6, epsrel=1e-6, limit=100)
                if not np.isfinite(integralA_val) or abs(integ_err) > 1e-2: integralA_val = ERROR_PENALTY
                integralA = coeffA * integralA_val
            except Exception: integralA = ERROR_PENALTY
        if integralA <= ERROR_PENALTY + 1: return ERROR_PENALTY
        integralB = 0.0
        def integrand_phi5_B(t_star):
            t_star_safe = max(t_star, EPS)
            factor = 1.0
            if t_star_safe < 1.0 - EPS:
                 base = 1.0 - t_star_safe
                 try: factor = 1.0 - base**(m - 1)
                 except Exception: factor = 1.0
                 if not np.isfinite(factor): factor = 1.0
            val = factor * (tau / t_star_safe)
            return val if np.isfinite(val) else 0.0
        try:
            integralB_val, integ_err = quad(integrand_phi5_B, tau, 1.0, epsabs=1e-6, epsrel=1e-6, limit=100)
            if not np.isfinite(integralB_val) or abs(integ_err) > 1e-2: integralB_val = ERROR_PENALTY
            integralB = integralB_val
        except Exception: integralB = ERROR_PENALTY
        if integralB <= ERROR_PENALTY + 1: return ERROR_PENALTY
        term1 = coeff1 * (integralA + integralB)
        if not np.isfinite(term1): term1 = ERROR_PENALTY
    if term1 <= ERROR_PENALTY + 1: return ERROR_PENALTY
    term2 = 0.0
    if p_0_eff > EPS:
        if tau < EPS or tau >= 1.0 - EPS: term2 = 0.0
        else:
            try:
                const_base = 1.0 - tau
                const_pow = const_base**(m - 1) if m > 1 else 1.0
                if not np.isfinite(const_pow): const_pow = 0.0
                const_factor = 1.0 - const_pow
                integralC_base = tau * np.log(1.0 / tau)
                if not np.isfinite(integralC_base): integralC_base = ERROR_PENALTY
                if integralC_base <= ERROR_PENALTY + 1: term2 = ERROR_PENALTY
                else: term2 = p_0_eff * const_factor * integralC_base
            except Exception: term2 = ERROR_PENALTY
    if term2 <= ERROR_PENALTY + 1: return ERROR_PENALTY
    result = term1 + term2
    return result if np.isfinite(result) else ERROR_PENALTY

# --- phi6_new (CORRECTED) ---
def phi6_new(p_0, p_1, p_2, p_3, tau, O, m, theta):
    # Formula: p_0*(DblIntegral + TermB) + p_1*TermC + (1-p_0-p_1)*(IntegralD_Combined)
    if m < M_MIN_phi_other: return ERROR_PENALTY
    p_0_eff = np.clip(p_0, 0.0, 1.0)
    p_1_eff = np.clip(p_1, 0.0, 1.0)
    p_2_eff = np.clip(p_2, 0.0, 1.0)
    coeff3 = 1.0 - p_0_eff - p_1_eff
    if coeff3 < -EPS: return ERROR_PENALTY
    coeff3 = max(0.0, coeff3)
    if not (EPS <= tau <= 1.0 - EPS): return ERROR_PENALTY

    term1 = 0.0
    if p_0_eff > EPS:
        dbl_integral_val = 0.0
        def phi6_inner_integrand(tm, t_star):
            t_star_safe = max(t_star, tau, EPS)
            tm_safe = np.clip(tm, 0.0, tau)
            denom = t_star_safe - tm_safe
            if abs(denom) < EPS: return 0.0
            pow_tm = 0.0
            if tm_safe < 1.0 - EPS:
                base_tm = 1.0 - tm_safe
                try: pow_tm = base_tm**(m - 1)
                except Exception: pow_tm = 0.0
                if not np.isfinite(pow_tm): pow_tm = 0.0
            fraction = (tau - tm_safe) / denom
            integrand = m * pow_tm * fraction
            return integrand if np.isfinite(integrand) else 0.0
        try:
            dbl_integral_res, dbl_int_err = dblquad(
                phi6_inner_integrand, tau, 1.0, lambda t_star: 0.0, lambda t_star: tau,
                epsabs=1e-5, epsrel=1e-5)
            if not np.isfinite(dbl_integral_res) or abs(dbl_int_err) > 1e-2:
                dbl_integral_val = ERROR_PENALTY
            else: dbl_integral_val = dbl_integral_res
        except Exception as e: dbl_integral_val = ERROR_PENALTY
        if dbl_integral_val <= ERROR_PENALTY + 1: return ERROR_PENALTY
        termB = 0.0
        try:
            one_minus_tau_m = (max(0, 1.0 - tau))**m
            termB = one_minus_tau_m * THETA_FACTOR
            if not np.isfinite(termB): termB = ERROR_PENALTY
        except Exception: termB = ERROR_PENALTY
        if termB <= ERROR_PENALTY + 1: return ERROR_PENALTY
        term1 = p_0_eff * (dbl_integral_val + termB)
        if not np.isfinite(term1): term1 = ERROR_PENALTY
    if term1 <= ERROR_PENALTY + 1: return ERROR_PENALTY

    term2 = p_1_eff * THETA_FACTOR
    if not np.isfinite(term2): term2 = ERROR_PENALTY
    if term2 <= ERROR_PENALTY + 1: return ERROR_PENALTY

    term3 = 0.0
    if coeff3 > EPS:
        integralD_combined = 0.0
        def integrand_phi6_D_combined(t):
            t_safe = max(t, EPS)
            pow_t_m = 0.0
            factor1 = 1.0
            if t_safe < 1.0 - EPS:
                base = 1.0 - t_safe
                try:
                    pow_t_m = base**m
                    if not np.isfinite(pow_t_m): pow_t_m = 0.0
                    factor1 = 1.0 - pow_t_m
                except Exception:
                    pow_t_m = 0.0
                    factor1 = 1.0
            termD_part1 = factor1 * ( (tau / t_safe) + p_2_eff * ((t_safe - tau) / t_safe) * THETA_FACTOR )
            termD_part2 = pow_t_m * THETA_FACTOR
            res = termD_part1 + termD_part2
            return res if np.isfinite(res) else 0.0
        try:
            integralD_val, integ_err = quad(integrand_phi6_D_combined, tau, 1.0, epsabs=1e-6, epsrel=1e-6, limit=100)
            if not np.isfinite(integralD_val) or abs(integ_err) > 1e-2:
                 integralD_val = ERROR_PENALTY
            integralD_combined = integralD_val
        except Exception as e: integralD_combined = ERROR_PENALTY
        if integralD_combined <= ERROR_PENALTY + 1: return ERROR_PENALTY
        term3 = coeff3 * integralD_combined
        if not np.isfinite(term3): term3 = ERROR_PENALTY
    if term3 <= ERROR_PENALTY + 1: return ERROR_PENALTY

    result = term1 + term2 + term3
    return result if np.isfinite(result) else ERROR_PENALTY

# --- End Phi Function Definitions ---


# --- Objective Function (Considers 6 phis) ---
def objective_function(params, m_values_phi2, m_values_other, theta):
    p_0, p_1, p_2, p_3, tau, O = params
    if not (0.0 <= p_0 <= 1.0 + EPS and \
            0.0 <= p_1 <= 1.0 + EPS and \
            p_0 + p_1 <= 1.0 + EPS and \
            0.0 <= p_2 <= 1.0 + EPS and \
            0.0 <= p_3 <= 1.0 + EPS and \
            EPS <= tau <= 1.0 - 2*EPS and \
            tau + EPS < O <= 1.0 - EPS):
        return -ERROR_PENALTY
    min_phi_values_across_m = [np.inf] * 6
    phi_functions = [phi1_new, phi2_new, phi3_new, phi4_new, phi5_new, phi6_new]
    m_sets = [m_values_other, m_values_phi2, m_values_other, m_values_other, m_values_other, m_values_other]
    for i in range(6):
        current_phi_i_min = np.inf
        m_values_to_check = m_sets[i]
        for m in m_values_to_check:
            value = phi_functions[i](p_0, p_1, p_2, p_3, tau, O, m, theta)
            current_phi_i_min = min(current_phi_i_min, value)
            if current_phi_i_min <= ERROR_PENALTY + 1:
                current_phi_i_min = ERROR_PENALTY
                break
        min_phi_values_across_m[i] = current_phi_i_min
        if current_phi_i_min <= ERROR_PENALTY + 1:
             min_overall = ERROR_PENALTY
             return -min_overall
    min_overall = min(min_phi_values_across_m)
    if min_overall <= ERROR_PENALTY + 1: return -ERROR_PENALTY
    return -min_overall

# --- Optimization Setup (6 variables) ---
bounds = [
    (0.0, 1.0),       # p_0
    (0.0, 1.0),       # p_1
    (0.0, 1.0),       # p_2
    (0.0, 1.0),       # p_3
    (EPS, 1.0 - 2*EPS),# tau
    (EPS, 1.0 - EPS)  # O
]

# --- Run the optimization ---
# *** Updated print statement for m range ***
print(f"Optimizing for theta = {THETA}, m = 1..20 (phi2: 2..20)")
print(f"Hyperparameters: p_0, p_1, p_2, p_3, tau, O")
print("Objective: Maximize min(min_m phi1_new, ..., min_m phi6_new)")
print("Running differential_evolution...")

start_time = time.time()

result = differential_evolution(
    objective_function,
    bounds,
    args=(M_VALUES_phi2, M_VALUES_other, THETA),
    strategy='best1bin',
    maxiter=1000, # Keep exhaustive settings
    popsize=40,   # Keep popsize for 6 variables
    tol=1e-5,
    mutation=(0.5, 1.5),
    recombination=0.7,
    seed=42,
    disp=True,
    updating='deferred',
    workers=-1
)

end_time = time.time()
print(f"\nOptimization finished in {end_time - start_time:.2f} seconds.")

# --- Print Results (Updated for 6 variables and 6 phis) ---
if result.success:
    print("\nOptimization successful!")
    optimal_params = result.x
    optimal_p0 = np.clip(optimal_params[0], 0.0, 1.0)
    optimal_p1 = np.clip(optimal_params[1], 0.0, 1.0)
    if optimal_p0 + optimal_p1 > 1.0:
        scale = 1.0 / (optimal_p0 + optimal_p1 + EPS)
        optimal_p0 *= scale; optimal_p1 *= scale
        print("Note: p0 and p1 scaled down slightly to meet p0+p1 <= 1 constraint.")
        optimal_p0 = np.clip(optimal_p0, 0.0, 1.0); optimal_p1 = np.clip(optimal_p1, 0.0, 1.0)
    optimal_p2 = np.clip(optimal_params[2], 0.0, 1.0)
    optimal_p3 = np.clip(optimal_params[3], 0.0, 1.0)
    optimal_O = np.clip(optimal_params[5], EPS, 1.0 - EPS)
    optimal_tau = np.clip(optimal_params[4], EPS, optimal_O - EPS)
    optimal_tau = np.clip(optimal_tau, EPS, 1.0 - 2*EPS)
    optimal_O = max(optimal_O, optimal_tau + EPS); optimal_O = min(optimal_O, 1.0 - EPS)
    optimal_params_cleaned = [optimal_p0, optimal_p1, optimal_p2, optimal_p3, optimal_tau, optimal_O]

    max_min_phi_optimizer = -result.fun

    print(f"\nOptimal Hyperparameters (cleaned):")
    print(f"  p_0  = {optimal_params_cleaned[0]:.6f}")
    print(f"  p_1  = {optimal_params_cleaned[1]:.6f}")
    print(f"  p_2  = {optimal_params_cleaned[2]:.6f}")
    print(f"  p_3  = {optimal_params_cleaned[3]:.6f}")
    print(f"  tau  = {optimal_params_cleaned[4]:.6f}")
    print(f"  O    = {optimal_params_cleaned[5]:.6f}")

    # --- Verification Step (Updated for 6 phis) ---
    print("\nVerifying result with optimal parameters:")
    min_phi_worst_values = [np.inf] * 6
    phi_functions = [phi1_new, phi2_new, phi3_new, phi4_new, phi5_new, phi6_new]
    m_sets = [m_values_other, m_values_phi2, m_values_other, m_values_other, m_values_other, m_values_other]
    worst_m_for_phi = [-1] * 6
    all_phis_valid = True
    print("Calculating min_m(phi_i) for each i=1..6:")
    for i in range(6):
        current_phi_i_min = np.inf; current_worst_m = -1
        m_values_to_check = m_sets[i]
        for m in m_values_to_check:
            value = phi_functions[i](*optimal_params_cleaned, m, THETA)
            if value < current_phi_i_min: current_phi_i_min = value; current_worst_m = m
            if value <= ERROR_PENALTY + 1:
                all_phis_valid = False; current_phi_i_min = ERROR_PENALTY; current_worst_m = m
                break
        min_phi_worst_values[i] = current_phi_i_min; worst_m_for_phi[i] = current_worst_m
        phi_i_min_str = f"{current_phi_i_min:.6f}" if current_phi_i_min > ERROR_PENALTY + 1 else "ERROR"
        print(f"  min_m phi{i+1}(m) = {phi_i_min_str} (worst m = {current_worst_m})")

    overall_min_check = min(min_phi_worst_values)
    if not all_phis_valid: print(f"\nWARNING: Some phi calculations hit penalty ({ERROR_PENALTY}). Result ({overall_min_check:.6f}) might reflect penalty.")
    elif overall_min_check <= ERROR_PENALTY + 1: print(f"\nWARNING: Recalculated minimum ({overall_min_check:.6f}) is penalty value.")
    else: print(f"\nRecalculated objective value = min(min_m phi1, ..., min_m phi6) = {overall_min_check:.6f}")

    print(f"Optimizer objective value = {max_min_phi_optimizer:.6f}")
    if all_phis_valid and overall_min_check > ERROR_PENALTY + 1:
        if not np.isclose(overall_min_check, max_min_phi_optimizer, rtol=1e-3, atol=1e-4):
             print("WARNING: Optimizer value differs significantly from verification calculation.")
        limiting_phi_index = np.argmin(min_phi_worst_values)
        print(f"The overall minimum was determined by phi{limiting_phi_index+1} (at its worst m={worst_m_for_phi[limiting_phi_index]})")

else:
    # --- Failure reporting ---
    print("\nOptimization failed or did not converge (even with increased effort).")
    print(f"Message: {result.message}")
    if hasattr(result, 'x'):
        optimal_params = result.x; max_min_phi = -result.fun
        print(f"Best parameters found (may be invalid):")
        print(f"  p_0={optimal_params[0]:.6f}, p_1={optimal_params[1]:.6f}, p_2={optimal_params[2]:.6f}, p_3={optimal_params[3]:.6f}, tau={optimal_params[4]:.6f}, O={optimal_params[5]:.6f}")
        print(f"Corresponding objective value = {-max_min_phi:.6f} (maximize this)")
    print("Consider further increasing maxiter/popsize, trying different strategies,")
    print("or checking the phi functions for extreme sensitivity or numerical issues.")