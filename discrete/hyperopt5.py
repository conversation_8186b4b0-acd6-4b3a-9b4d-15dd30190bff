import numpy as np
from scipy.integrate import quad, dblquad
from scipy.optimize import minimize
import math
import warnings

# Fixed parameter
THETA = 0.627
CONST_FACTOR = (1 - THETA) / (1 + THETA) # 0.25

# m ranges
M_RANGE_FULL = range(1, 51) # m = 1 to 20
M_RANGE_CASE3 = range(2, 51) # m = 2 to 20
# M_RANGE_CASE4_GT1 is no longer needed in the same way

# --- Define Functions for Each Case ---
# NOTE: All functions now accept p4, even if unused, for consistency

# Cases 5.1 and 5.2 (Unchanged, added p4 arg)
def calculate_case1_2(tau, p0, p1, p3, p4):
    prob_T_1 = max(0, 1 - p0 - p1)
    return prob_T_1 * tau + p1

# Case 5.3 (Unchanged, added p4 arg)
def calculate_case3(tau, p0, p1, p3, p4, m):
    if m < 2:
        return np.inf
    prob_T_tau = p0
    prob_T_1 = max(0, 1 - p0 - p1)
    term_tau = 0
    factor = 1
    if prob_T_tau > 1e-9 and tau > 1e-9 and tau < 1.0 - 1e-9:
         term_tau = prob_T_tau * factor * tau * math.log(1.0 / tau)
    term_1 = 0
    if prob_T_1 > 1e-9 and tau < 1.0 - 1e-9:
        term_1_integrand = lambda t: (1 - (1 - t)**(m - 1)) * (tau / t) + (1 - t)**(m - 1)
        try:
            integral_1, err_1 = quad(term_1_integrand, tau, 1.0, epsabs=1e-6, epsrel=1e-6)
            if err_1 > 1e-3:
                 warnings.warn(f"High integration error ({err_1:.2e}) in Case 3 for m={m}, tau={tau:.3f}")
            term_1 = prob_T_1 * integral_1
        except Exception as e:
            warnings.warn(f"Integration failed in Case 3 for m={m}, tau={tau:.3f}: {e}")
            return -np.inf
    return term_tau + term_1

# --- Case 5.4: REVISED Function ---
def calculate_case4_revised(tau, p0, p1, p3, p4, m):
    """
    Implements the revised logic for Case 5.4 based on m=1, m=2, m>=3.
    """
    prob_T_tau = p0
    prob_T_1 = max(0, 1 - p0 - p1)
    term_tau = 0.0
    term_1 = 0.0

    # --- T <= tau part (contributes term_tau, depends on p0) ---
    if prob_T_tau > 1e-9 and tau > 1e-9 and tau < 1.0 - 1e-9:
        if m == 1:
            # Integrand: tau/t + p3 * (1 - tau/t) * CONST_FACTOR
            def integrand_m1_tau(t):
                if abs(t) < 1e-12: return 0
                return tau / t + p3 * (1 - tau / t) * CONST_FACTOR
            try:
                integral_m1, err_m1 = quad(integrand_m1_tau, tau, 1.0, epsabs=1e-6, epsrel=1e-6)
                if err_m1 > 1e-3:
                    warnings.warn(f"High integration error ({err_m1:.2e}) in Case 4 (m=1, T=tau), tau={tau:.3f}")
                term_tau = prob_T_tau * integral_m1
            except Exception as e:
                 warnings.warn(f"Integration failed in Case 4 (m=1, T=tau), tau={tau:.3f}: {e}")
                 return -np.inf # Indicate failure

        elif m == 2:
            # Integrand: A*(τ/t) + B*(τ/t + p3*p4*(1-τ/t)*CONST) + C*(1-p3)*(τ/t)
            # Where A=τ, B=t-τ, C=1-t
            def integrand_m2_tau(t):
                if abs(t) < 1e-12: return 0
                t = min(t, 1.0) # Clip t just in case quad goes slightly over
                A = tau
                B = max(0, t - tau) # Ensure B >= 0
                C = max(0, 1 - t)   # Ensure C >= 0

                termA = A * (tau / t)
                termB = B * (tau / t + p3 * p4 * (1 - tau / t) * CONST_FACTOR)
                termC = C * (1 - p3) * (tau / t)
                return termA + termB + termC
            try:
                integral_m2, err_m2 = quad(integrand_m2_tau, tau, 1.0, epsabs=1e-6, epsrel=1e-6)
                if err_m2 > 1e-3:
                     warnings.warn(f"High integration error ({err_m2:.2e}) in Case 4 (m=2, T=tau), tau={tau:.3f}")
                term_tau = prob_T_tau * integral_m2
            except Exception as e:
                 warnings.warn(f"Integration failed in Case 4 (m=2, T=tau), tau={tau:.3f}: {e}")
                 return -np.inf

        else: # m >= 3
            # Integrand: A*(τ/t) + B*(τ/t)*(1-p3*p4) + C*(1-p3)*(τ/t)
            # Where A=1-(1-τ)^(m-1), B=(1-(1-t)^(m-1))-A, C=(1-t)^(m-1)
            def integrand_m_ge3_tau(t):
                if abs(t) < 1e-12: return 0
                t = min(t, 1.0) # Clip t
                
                A = 1.0 - (1.0 - tau)**(m - 1)
                one_minus_t_pow = (max(0, 1.0 - t))**(m - 1) # Ensure base >= 0
                B = (1.0 - one_minus_t_pow) - A
                C = one_minus_t_pow
                
                # Ensure B, C are non-negative (can happen due to float precision near boundaries)
                B = max(0, B)
                C = max(0, C)

                termA = A * (tau / t)
                termB = B * (tau / t) * (1 - p3 * p4)
                termC = C * (1 - p3) * (tau / t)
                return termA + termB + termC
            try:
                integral_m_ge3, err_m_ge3 = quad(integrand_m_ge3_tau, tau, 1.0, epsabs=1e-6, epsrel=1e-6)
                if err_m_ge3 > 1e-3:
                     warnings.warn(f"High integration error ({err_m_ge3:.2e}) in Case 4 (m>=3, T=tau), m={m}, tau={tau:.3f}")
                term_tau = prob_T_tau * integral_m_ge3
            except Exception as e:
                 warnings.warn(f"Integration failed in Case 4 (m>=3, T=tau), m={m}, tau={tau:.3f}: {e}")
                 return -np.inf

    # --- T > tau part (contributes term_1, depends on 1-p0-p1) ---
    if prob_T_1 > 1e-9 and tau < 1.0 - 1e-9:
        if m == 1:
            # Formula explicitly gives 0 for m=1 when T > tau
            term_1 = 0.0
        else: # m >= 2 (covers m=2 and m>=3 cases from formula)
             # Integrand: (1 - (1-t)**(m-1)) * (tau/t)
            def integrand_m_gt1_one(t):
                if abs(t) < 1e-12: return 0
                t = min(t, 1.0)
                pow_term = (max(0, 1.0 - t))**(m - 1)
                return (1.0 - pow_term) * (tau / t)
            try:
                integral_gt1_one, err_gt1_one = quad(integrand_m_gt1_one, tau, 1.0, epsabs=1e-6, epsrel=1e-6)
                if err_gt1_one > 1e-3:
                     warnings.warn(f"High integration error ({err_gt1_one:.2e}) in Case 4 (m>1, T>tau), m={m}, tau={tau:.3f}")
                term_1 = prob_T_1 * integral_gt1_one
            except Exception as e:
                 warnings.warn(f"Integration failed in Case 4 (m>1, T>tau), m={m}, tau={tau:.3f}: {e}")
                 return -np.inf # Indicate failure

    result = term_tau + term_1
    if not np.isfinite(result):
        warnings.warn(f"Non-finite result in calculate_case4_revised for m={m}, tau={tau:.3f}, p0={p0:.3f}, p1={p1:.3f}")
        return -np.inf
    return result


# Case 5.5 (Unchanged, added p4 arg)
def calculate_case5(tau, p0, p1, p3, p4, m):
    prob_T_tau = p0
    prob_T_1 = max(0, 1 - p0 - p1)
    term_1 = 0
    if prob_T_1 > 1e-9 and tau < 1.0 - 1e-9:
        integral_1_val = 0
        if m > 0:
             integral_1_val = (1 - tau)**m / m
        integral_1_part2_val = 0
        def integrand_1_part2(t):
            if abs(t) < 1e-12: return 0
            term_pow = (max(0, 1.0 - t))**(m - 1) if m > 0 else 0
            if m == 1: term_pow = 1.0
            return (1.0 - term_pow) * (tau / t)
        try:
            integral_1_part2_val, err_1_p2 = quad(integrand_1_part2, tau, 1.0, epsabs=1e-6, epsrel=1e-6)
            if err_1_p2 > 1e-3:
                 warnings.warn(f"High integration error ({err_1_p2:.2e}) in Case 5 (T=1) for m={m}, tau={tau:.3f}")
        except Exception as e:
             warnings.warn(f"Integration failed in Case 5 (T=1) for m={m}, tau={tau:.3f}: {e}")
             return -np.inf
        term_1 = prob_T_1 * (integral_1_val + integral_1_part2_val)

    term_tau = 0
    if prob_T_tau > 1e-9 and tau > 1e-9 and tau < 1.0 - 1e-9:
        const_factor = 0.0
        if m > 1:
            const_factor = (1.0 - (1.0 - tau)**(m - 1)) * tau
        if abs(const_factor) > 1e-12:
             integral_tau_val = const_factor * math.log(1.0 / tau)
             term_tau = prob_T_tau * integral_tau_val
    result = term_1 + term_tau
    if not np.isfinite(result):
        warnings.warn(f"Non-finite result in calculate_case5 for m={m}, tau={tau:.3f}, p0={p0:.3f}, p1={p1:.3f}")
        return -np.inf
    return result


# Case 5.6 (Integrand unchanged, added p4 arg to main func)
def case6_double_integrand(tm, t_star, m, tau):
    if abs(t_star - tm) < 1e-12: return 0.0
    if tm < -1e-9 or tm > tau + 1e-9: return 0.0
    if m == 1: pow_term = 1.0
    else: pow_term = (max(0.0, 1.0 - tm))**(m - 1)
    numerator = m * pow_term * (tau - tm)
    denominator = t_star - tm
    val = numerator / denominator
    if not np.isfinite(val): return 0.0
    return val

def calculate_case6(tau, p0, p1, p3, p4, m):
    prob_T_tau = p0
    prob_T_eps = p1
    prob_T_1 = max(0, 1 - p0 - p1)
    term_tau = 0.0
    if prob_T_tau > 1e-9 and tau > 1e-9 and tau < 1.0 - 1e-9:
        double_integral_val = 0.0
        try:
            integral_val, integral_err = dblquad(
                case6_double_integrand, tau, 1.0, lambda t_star: 0.0, lambda t_star: tau,
                args=(m, tau), epsabs=1e-6, epsrel=1e-6
            )
            if integral_err > 5e-3:
                 warnings.warn(f"High double integration error ({integral_err:.2e}) in Case 6 (T=tau) for m={m}, tau={tau:.3f}")
            double_integral_val = integral_val
        except Exception as e:
            warnings.warn(f"Double integration failed in Case 6 (T=tau) for m={m}, tau={tau:.3f}: {e}")
            return -np.inf
        original_term = (1.0 - tau)**m * CONST_FACTOR
        term_tau = prob_T_tau * (double_integral_val + original_term)

    term_eps = prob_T_eps * CONST_FACTOR
    term_1 = 0.0
    if prob_T_1 > 1e-9 and tau < 1.0 - 1e-9:
        def integrand_1(t):
            if abs(t) < 1e-12: return 0
            pow_term = (max(0, 1.0 - t))**m
            return (1.0 - pow_term) * (tau / t) + pow_term * CONST_FACTOR
        try:
            integral_1_val, err_1 = quad(integrand_1, tau, 1.0, epsabs=1e-6, epsrel=1e-6)
            if err_1 > 1e-3:
                 warnings.warn(f"High integration error ({err_1:.2e}) in Case 6 (T=1) for m={m}, tau={tau:.3f}")
            term_1 = prob_T_1 * integral_1_val
        except Exception as e:
             warnings.warn(f"Integration failed in Case 6 (T=1) for m={m}, tau={tau:.3f}: {e}")
             return -np.inf
    total = term_tau + term_1 #+ term_eps
    if not np.isfinite(total):
        warnings.warn(f"Non-finite result in calculate_case6 for m={m}, tau={tau:.3f}, p0={p0:.3f}, p1={p1:.3f}")
        return -np.inf
    return total


# --- Objective Function (Updated for p4 and revised Case 4) ---

def objective_function(params):
    """
    Calculates the minimum competitive ratio across all cases.
    params: [tau, p0, p1, p3, p4]
    """
    # Unpack parameters including p4
    tau, p0, p1, p3, p4 = params

    # Basic validation
    if not (0 <= tau <= 1 and 0 <= p0 <= 1 and 0 <= p1 <= 1 and 0 <= p3 <= 1 and 0 <= p4 <= 1):
        return np.inf
    if p0 + p1 > 1.00001:
        return np.inf

    tau = np.clip(tau, 1e-7, 1.0 - 1e-7)

    results = []
    min_ratio = np.inf

    # --- Calculate results for each case, passing p4 ---

    # Case 1/2
    val_12 = calculate_case1_2(tau, p0, p1, p3, p4)
    if not np.isfinite(val_12): return np.inf
    min_ratio = min(min_ratio, val_12)

    # Case 3 (min over m=2..20)
    case3_vals = [calculate_case3(tau, p0, p1, p3, p4, m) for m in M_RANGE_CASE3]
    valid_case3_vals = [v for v in case3_vals if np.isfinite(v)]
    if not valid_case3_vals: return np.inf
    min_ratio = min(min_ratio, min(valid_case3_vals))

    # Case 4 (REVISED - min over m=1..20 using new function)
    case4_vals = [calculate_case4_revised(tau, p0, p1, p3, p4, m) for m in M_RANGE_FULL]
    valid_case4_vals = [v for v in case4_vals if np.isfinite(v)]
    if not valid_case4_vals: return np.inf
    min_ratio = min(min_ratio, min(valid_case4_vals))

    # Case 5 (min over m=1..20)
    case5_vals = [calculate_case5(tau, p0, p1, p3, p4, m) for m in M_RANGE_FULL]
    valid_case5_vals = [v for v in case5_vals if np.isfinite(v)]
    if not valid_case5_vals: return np.inf
    min_ratio = min(min_ratio, min(valid_case5_vals))

    # Case 6 (min over m=1..20)
    case6_vals = [calculate_case6(tau, p0, p1, p3, p4, m) for m in M_RANGE_FULL]
    valid_case6_vals = [v for v in case6_vals if np.isfinite(v)]
    if not valid_case6_vals: return np.inf
    min_ratio = min(min_ratio, min(valid_case6_vals))

    if not np.isfinite(min_ratio):
        return np.inf

    return -min_ratio

# --- Optimization (Updated for p4) ---

# Bounds for variables [tau, p0, p1, p3, p4]
bounds = [(1e-6, 1.0 - 1e-6), # tau
          (0.0, 1.0),        # p0
          (0.0, 1.0),        # p1
          (0.9999, 1.0),        # p3
          (0.9999, 1.0)]        # p4

# Constraints: p0 + p1 <= 1
constraints = ({'type': 'ineq', 'fun': lambda p: 1.0 - p[1] - p[2]}) # p[1]=p0, p[2]=p1
import random
# Initial guess [tau, p0, p1, p3, p4] - Add a guess for p4
initial_guess = [0.4+random.uniform(-0.05,0.05), 0.1+random.uniform(-0.05,0.05), 0.03+random.uniform(-0.01,0.05), 1, 1]

print("Starting optimization (Revised Case 4, includes p4)...")
print(f"Theta = {THETA}, Const Factor = {CONST_FACTOR:.4f}")
print(f"Initial Guess: {initial_guess}")
print(f"Bounds: {bounds}")
print(f"Constraints: p0 + p1 <= 1")
print("-" * 30)

# Suppress warnings during optimization (optional)
# warnings.filterwarnings("ignore")

# Using SLSQP method
result = minimize(objective_function,
                  initial_guess,
                  method='SLSQP',
                  bounds=bounds,
                  constraints=constraints,
                  options={'disp': True, 'maxiter': 20000, 'ftol': 1e-7}, )

# Restore warnings
# warnings.filterwarnings("default")

print("-" * 30)

# --- Reporting (Updated for p4 and revised Case 4) ---
if result.success:
    optimal_params = result.x
    max_min_ratio = -result.fun

    print("Optimization Successful!")
    print(f"Optimal tau: {optimal_params[0]:.6f}")
    print(f"Optimal p0:  {optimal_params[1]:.6f}")
    print(f"Optimal p1:  {optimal_params[2]:.6f}")
    print(f"Optimal p3:  {optimal_params[3]:.6f}")
    print(f"Optimal p4:  {optimal_params[4]:.6f}") # Report p4
    print(f"Prob T=1:    {max(0, 1 - optimal_params[1] - optimal_params[2]):.6f}")
    print(f"Maximum Minimum Competitive Ratio: {max_min_ratio:.6f}")

    print("-" * 30)
    print("Competitive ratios for each case at optimum:")
    tau_opt, p0_opt, p1_opt, p3_opt, p4_opt = optimal_params # Unpack p4
    tau_opt = np.clip(tau_opt, 1e-7, 1.0 - 1e-7)
    all_calculated_crs = []

    # --- Case 1/2 ---
    try:
        cr12 = calculate_case1_2(tau_opt, p0_opt, p1_opt, p3_opt, p4_opt) # Pass p4
        if np.isfinite(cr12):
            print(f"Case 1/2: {cr12:.6f}")
            all_calculated_crs.append(cr12)
        else: print("Case 1/2: Calculation resulted in non-finite value at optimum.")
    except Exception as e: print(f"Case 1/2: Error during final check: {e}")

    # --- Case 3 ---
    try:
        case3_vals = [calculate_case3(tau_opt, p0_opt, p1_opt, p3_opt, p4_opt, m) for m in M_RANGE_CASE3] # Pass p4
        valid_case3_vals = [v for v in case3_vals if np.isfinite(v)]
        if valid_case3_vals:
            cr3 = min(valid_case3_vals)
            print(f"Case 3:   {cr3:.6f} (worst m in {M_RANGE_CASE3})")
            all_calculated_crs.append(cr3)
        else: print(f"Case 3:   Failed calculation or no valid m at optimum (m in {M_RANGE_CASE3})")
    except Exception as e: print(f"Case 3:   Error during final check: {e}")

    # --- Case 4 (Revised) ---
    try:
        # Use the revised function
        case4_vals = [calculate_case4_revised(tau_opt, p0_opt, p1_opt, p3_opt, p4_opt, m) for m in M_RANGE_FULL] # Pass p4
        valid_case4_vals = [v for v in case4_vals if np.isfinite(v)]
        if valid_case4_vals:
            cr4 = min(valid_case4_vals)
            #for i in M_RANGE_FULL:
                #print(f"Case 4:   {case4_vals[i]:.6f} (m={i})")
            print(f"Case 4:   {cr4:.6f} (worst m in {M_RANGE_FULL})")
            all_calculated_crs.append(cr4)
        else: print(f"Case 4:   Failed calculation or no valid m at optimum (m in {M_RANGE_FULL})")
    except Exception as e: print(f"Case 4:   Error during final check: {e}")

    # --- Case 5 ---
    try:
        case5_vals = [calculate_case5(tau_opt, p0_opt, p1_opt, p3_opt, p4_opt, m) for m in M_RANGE_FULL] # Pass p4
        valid_case5_vals = [v for v in case5_vals if np.isfinite(v)]
        if valid_case5_vals:
            cr5 = min(valid_case5_vals)
            print(f"Case 5:   {cr5:.6f} (worst m in {M_RANGE_FULL})")
            all_calculated_crs.append(cr5)
        else: print(f"Case 5:   Failed calculation or no valid m at optimum (m in {M_RANGE_FULL})")
    except Exception as e: print(f"Case 5:   Error during final check: {e}")

    # --- Case 6 ---
    try:
        case6_vals = [calculate_case6(tau_opt, p0_opt, p1_opt, p3_opt, p4_opt, m) for m in M_RANGE_FULL] # Pass p4
        valid_case6_vals = [v for v in case6_vals if np.isfinite(v)]
        if valid_case6_vals:
            cr6 = min(valid_case6_vals)
            print(f"Case 6:   {cr6:.6f} (worst m in {M_RANGE_FULL})")
            all_calculated_crs.append(cr6)
        else: print(f"Case 6:   Failed calculation or no valid m at optimum (m in {M_RANGE_FULL})")
    except Exception as e: print(f"Case 6:   Error during final check: {e}")

    # --- Final Minimum Check ---
    if all_calculated_crs:
        final_min = min(all_calculated_crs)
        print(f"Min Check:{final_min:.6f} (should match max_min_ratio)")
    else: print("Min Check: Could not calculate minimum ratio from individual cases.")

else: # If optimization failed
    print("Optimization Failed!")
    print(f"Message: {result.message}")
    print(f"Final function value (negative min ratio): {result.fun}")
    print(f"Final parameters: {result.x}")
    print("Failure might be due to numerical instability or complexity.")