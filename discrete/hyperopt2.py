import numpy as np
from scipy.integrate import quad, dblquad
from scipy.optimize import differential_evolution
import warnings
import math
import time
from numba import njit # <--- Import Numba

# --- Constants and Setup ---
THETA = 0.6

EPS = 1e-9
M_MIN_phi2 = 2
M_MIN_phi_other = 1
M_MAX_INT = 20
M_VALUES_phi2 = list(range(M_MIN_phi2, M_MAX_INT + 1)) # 2 to 20
M_VALUES_other = list(range(M_MIN_phi_other, M_MAX_INT + 1)) # 1 to 20

ERROR_PENALTY = -1e18
# Pre-calculate theta factor
THETA_FACTOR = (1.0 - THETA) / (1.0 + THETA) if abs(1.0 + THETA) > EPS else 0

# --- Numba-compiled Integrands ---

@njit # Compile with Numba
def integrand_phi2_numba(t, tau, m, p_2_eff):
    # N<PERSON> doesn't like np.clip directly in args, handle inside
    t_safe = max(t, EPS)
    factor_pow = 0.0
    if t_safe >= 1.0 - EPS:
         # For m=2, (1-t)^(2-2)=1. For m>2, (1-t)^(m-2) -> 0.
         # Let's follow original logic precisely:
         factor_pow = 1.0 if m == 2 else 0.0
    elif t_safe < 1.0 - EPS : # Check needed as base could be negative otherwise? Base is >= EPS
         base = 1.0 - t_safe
         # Numba handles potential errors in pow differently, be explicit
         if base > 0:
             try: # Numba supports try/except for specific exceptions
                 factor_pow = base**(m - 2)
             except:
                 factor_pow = 0.0 # Or handle error appropriately
         else:
              factor_pow = 0.0 # Should not happen if t_safe < 1.0
    # Avoid division by zero if t_safe is EPS
    tau_div_t = (tau / t_safe) if t_safe > EPS else 0.0
    term_a = (1.0 - factor_pow) * tau_div_t
    term_b = factor_pow * (1.0 - p_2_eff)
    # Numba requires explicit finite checks sometimes
    res = term_a + term_b
    return res if np.isfinite(res) else 0.0

# Note: phi3 term 2 now analytical

@njit # Compile with Numba
def common_factor_phi4_numba(t_star, m_val):
     if t_star >= 1.0 - EPS: return 1.0
     t_star_safe = max(t_star, EPS) # Ensure t_star_safe >= EPS
     # Numba pow requires careful handling
     base = 1.0 - t_star_safe
     pow_val = 0.0
     if base > 0:
         try:
             pow_val = base**(m_val - 1)
         except:
             pow_val = 0.0
     else: # base <= 0 should not happen if t_star_safe < 1.0
         pow_val = 0.0
     return 1.0 - pow_val # No need to check isfinite here, pow_val is float

@njit # Compile with Numba
def integrand_phi4_term1_numba(t_star, tau, m, p_3_eff):
    t_star_safe = max(t_star, EPS)
    factor = common_factor_phi4_numba(t_star_safe, m)
    # Avoid division by zero
    tau_div_t = (tau / t_star_safe) if t_star_safe > EPS else 0.0
    val = (p_3_eff * factor + (1.0 - p_3_eff)) * tau_div_t
    return val if np.isfinite(val) else 0.0

@njit # Compile with Numba
def integrand_phi4_term2_numba(t_star, tau, m):
    t_star_safe = max(t_star, EPS)
    factor = common_factor_phi4_numba(t_star_safe, m)
    # Avoid division by zero
    tau_div_t = (tau / t_star_safe) if t_star_safe > EPS else 0.0
    val = factor * tau_div_t
    return val if np.isfinite(val) else 0.0

# Note: phi5 integral A now analytical

@njit # Compile with Numba
def integrand_phi5_B_numba(t_star, tau, m):
    t_star_safe = max(t_star, EPS)
    factor = 1.0
    if t_star_safe < 1.0 - EPS:
         base = 1.0 - t_star_safe
         pow_val = 0.0
         if base > 0:
             try:
                 pow_val = base**(m - 1)
             except:
                 pow_val = 0.0
         else:
              pow_val = 0.0
         factor = 1.0 - pow_val
    # Avoid division by zero
    tau_div_t = (tau / t_star_safe) if t_star_safe > EPS else 0.0
    val = factor * tau_div_t
    return val if np.isfinite(val) else 0.0

@njit # Compile with Numba
def phi6_inner_integrand_numba(tm, t_star, tau, m):
    # Clip tm inside numba func
    tm_safe = min(max(tm, 0.0), tau)
    # Ensure t_star is at least tau
    t_star_safe = max(t_star, tau, EPS) # Redundant max with tau? dblquad ensures t_star >= tau

    denom = t_star_safe - tm_safe
    if abs(denom) < EPS: return 0.0

    pow_tm = 0.0
    base_tm = 1.0 - tm_safe
    if base_tm > EPS: # Check base > 0 for pow
        try:
            pow_tm = base_tm**(m - 1)
        except:
            pow_tm = 0.0
    # else: pow_tm remains 0.0

    fraction = (tau - tm_safe) / denom
    integrand = m * pow_tm * fraction
    return integrand if np.isfinite(integrand) else 0.0

@njit # Compile with Numba
def integrand_phi6_D_combined_numba(t, tau, m, p_2_eff, theta_factor_in):
    t_safe = max(t, EPS)
    pow_t_m = 0.0
    factor1 = 1.0
    base = 1.0 - t_safe
    if base > EPS: # Check base > 0 for pow
        try:
            pow_t_m = base**m
            factor1 = 1.0 - pow_t_m
        except:
            pow_t_m = 0.0
            factor1 = 1.0 # pow failed, assume it's like it went to 0? Or keep 1? Original had 1.0.
    elif base <= EPS: # If base is zero or negative
         pow_t_m = 0.0 # Base is 0 or negative
         factor1 = 1.0 # (1-0)=1
         if m == 0: pow_t_m = 1.0 # 0^0 = 1? Let's stick to original logic: if t_safe >= 1-EPS, pow=0.

    if t_safe >= 1.0 - EPS: # Overwrite based on original logic if near 1
        pow_t_m = 0.0
        factor1 = 1.0

    # Avoid division by zero
    tau_div_t = (tau / t_safe) if t_safe > EPS else 0.0
    t_minus_tau_div_t = ((t_safe - tau) / t_safe) if t_safe > EPS else 0.0

    termD_part1 = factor1 * ( tau_div_t + p_2_eff * t_minus_tau_div_t * theta_factor_in )
    termD_part2 = pow_t_m * theta_factor_in
    res = termD_part1 + termD_part2
    return res if np.isfinite(res) else 0.0


# --- Phi Function Definitions (Updated) ---

# --- phi1_new (mostly unchanged) ---
def phi1_new(p_0, p_1, p_2, p_3, tau, O, m, theta):
    p_0_eff = np.clip(p_0, 0.0, 1.0)
    p_1_eff = np.clip(p_1, 0.0, 1.0)
    coeff = 1.0 - p_0_eff - p_1_eff
    if coeff < -EPS: return ERROR_PENALTY
    coeff = max(0.0, coeff) # No change needed here, simple check
    # Use math functions for scalars
    if not (EPS <= tau <= 1.0 - EPS): return ERROR_PENALTY
    term1 = coeff * tau
    val = term1 + p_1_eff
    return val # isfinite check is implicit via ERROR_PENALTY return

# --- phi2_new (using Numba integrand) ---
def phi2_new(p_0, p_1, p_2, p_3, tau, O, m, theta):
    if m < M_MIN_phi2: return ERROR_PENALTY
    p_0_eff = np.clip(p_0, 0.0, 1.0)
    p_1_eff = np.clip(p_1, 0.0, 1.0)
    p_2_eff = np.clip(p_2, 0.0, 1.0)
    coeff = 1.0 - p_0_eff - p_1_eff
    if coeff < -EPS: return ERROR_PENALTY
    coeff = max(0.0, coeff)
    if not (EPS <= tau <= 1.0 - EPS): return ERROR_PENALTY

    term1 = 0.0
    if p_0_eff > EPS:
        # Use math.log for scalar
        if tau < EPS: term1 = ERROR_PENALTY # Avoid log(0)
        else:
            try:
                # Use math.log, ensure tau is not exactly 0
                log_val = math.log(1.0 / max(tau, EPS)) # max already handled
                term1 = p_0_eff * tau * log_val
                if not np.isfinite(term1): term1 = ERROR_PENALTY # Should be finite
            except Exception: term1 = ERROR_PENALTY # Catch potential math errors
    # Early exit if term1 fails
    if term1 <= ERROR_PENALTY + 1: return ERROR_PENALTY # Use "<=" to catch exact penalty

    term2 = 0.0
    if coeff > EPS:
        try:
            # Pass scalar arguments to Numba function
            integral_val, integ_err = quad(
                integrand_phi2_numba, tau, 1.0,
                args=(tau, m, p_2_eff), # Arguments for integrand_phi2_numba
                epsabs=1e-6, epsrel=1e-6, limit=100
            )
            # Check validity AFTER quad call
            if not np.isfinite(integral_val) or abs(integ_err) > 1e-2:
                 integral_val = ERROR_PENALTY # Assign penalty if invalid
            # Check before multiplication
            if integral_val <= ERROR_PENALTY + 1:
                 term2 = ERROR_PENALTY
            else:
                 term2 = coeff * integral_val

        except Exception as e:
            # print(f"WARN: quad failed in phi2: {e}") # Optional debug print
            term2 = ERROR_PENALTY # Quad itself failed
    # Early exit if term2 fails
    if term2 <= ERROR_PENALTY + 1: return ERROR_PENALTY

    result = term1 + term2
    # Final check, although intermediate checks should cover it
    return result if np.isfinite(result) else ERROR_PENALTY


# --- phi3_new (using analytical integral) ---
def phi3_new(p_0, p_1, p_2, p_3, tau, O, m, theta):
    p_0_eff = np.clip(p_0, 0.0, 1.0)
    p_3_eff = np.clip(p_3, 0.0, 1.0)
    if not (EPS <= tau <= 1.0 - EPS): return ERROR_PENALTY

    term1 = 0.0
    if p_0_eff > EPS:
        if tau < EPS or tau >= 1.0 - EPS: # Avoid log(0) or log(negative) if tau=1
             term1 = 0.0 # Assign 0, not penalty, based on original logic maybe? Check original. Original used integral val. Log(1/tau) ok near 1.
             # Let's stick to the original logic's effective behavior more closely:
             try:
                 # Check tau value before log
                 if tau < EPS: # log(1/small) -> large positive
                      # What should happen here? Original returns ERROR_PENALTY later maybe?
                      # If tau is tiny, log(1/tau) is large. Let's allow it.
                      # If tau >= 1.0 - EPS, let's set to 0?
                      # Let's re-read original: tau * log(1/tau) used.
                      # If tau is near 1, log(1/tau) is near 0. Result is near 0.
                      # If tau is near 0, tau*log(1/tau) -> 0.
                      # So the condition tau >= 1.0 - EPS seems irrelevant if using formula
                      if tau < EPS: return ERROR_PENALTY # Explicitly handle tau=0 case if needed

                      # Use math.log for scalar
                      log_val = math.log(1.0 / tau)
                      integral1_val = tau * log_val
                      if not np.isfinite(integral1_val): integral1_val = ERROR_PENALTY # Should be finite for tau in (EPS, 1-EPS)
                      term1 = p_0_eff * integral1_val
                 else: # tau is valid
                      log_val = math.log(1.0 / tau)
                      integral1_val = tau * log_val
                      if not np.isfinite(integral1_val): integral1_val = ERROR_PENALTY
                      term1 = p_0_eff * integral1_val

             except Exception: term1 = ERROR_PENALTY # Catch math errors
    # Early exit
    # Be careful with strict equality if ERROR_PENALTY is exact
    if term1 <= ERROR_PENALTY + 1: return ERROR_PENALTY

    term2 = 0.0
    if p_3_eff > EPS:
        # *** Use Analytical Solution ***
        try:
            if tau < EPS or tau >= 1.0: # Avoid log(0) or invalid range
                integral2_val = ERROR_PENALTY # Or 0? If tau=1, range is zero. If tau=0, log(0) invalid.
            else:
                log_tau = math.log(tau)
                # Formula: THETA_FACTOR * [1.0 - tau + tau * log(tau)]
                integral2_val = THETA_FACTOR * (1.0 - tau + tau * log_tau)

            if not np.isfinite(integral2_val): integral2_val = ERROR_PENALTY # Check result
            # Check before multiply
            if integral2_val <= ERROR_PENALTY + 1:
                 term2 = ERROR_PENALTY
            else:
                 term2 = p_3_eff * integral2_val
        except Exception:
             term2 = ERROR_PENALTY # Catch math errors like log(0) if bounds fail
    # Early exit
    if term2 <= ERROR_PENALTY + 1: return ERROR_PENALTY

    result = term1 + term2
    return result if np.isfinite(result) else ERROR_PENALTY


# --- phi4_new (using Numba integrands) ---
def phi4_new(p_0, p_1, p_2, p_3, tau, O, m, theta):
    if m < M_MIN_phi_other: return ERROR_PENALTY
    p_0_eff = np.clip(p_0, 0.0, 1.0)
    p_1_eff = np.clip(p_1, 0.0, 1.0)
    p_2_eff = np.clip(p_2, 0.0, 1.0)
    p_3_eff = np.clip(p_3, 0.0, 1.0)
    coeff2 = 1.0 - p_0_eff - p_1_eff # Calculate once
    if coeff2 < -EPS: return ERROR_PENALTY
    coeff2 = max(0.0, coeff2)
    if not (EPS <= tau <= 1.0 - EPS): return ERROR_PENALTY # Check tau range

    term1 = 0.0
    if p_0_eff > EPS:
        try:
            integral1_val, integ_err = quad(
                integrand_phi4_term1_numba, tau, 1.0,
                args=(tau, m, p_3_eff),
                epsabs=1e-6, epsrel=1e-6, limit=100
            )
            if not np.isfinite(integral1_val) or abs(integ_err) > 1e-2:
                 integral1_val = ERROR_PENALTY
            # Check before multiply
            if integral1_val <= ERROR_PENALTY + 1:
                 term1 = ERROR_PENALTY
            else:
                 term1 = p_0_eff * integral1_val
        except Exception as e:
            # print(f"WARN: quad failed in phi4 term1: {e}")
            term1 = ERROR_PENALTY
    # Early exit
    if term1 <= ERROR_PENALTY + 1: return ERROR_PENALTY

    term2 = 0.0
    coeff_term2 = coeff2 * (1.0 - p_2_eff) # Precalculate coefficient
    if coeff_term2 > EPS:
        try:
            integral2_val, integ_err = quad(
                integrand_phi4_term2_numba, tau, 1.0,
                args=(tau, m),
                epsabs=1e-6, epsrel=1e-6, limit=100
            )
            if not np.isfinite(integral2_val) or abs(integ_err) > 1e-2:
                 integral2_val = ERROR_PENALTY
            # Check before multiply
            if integral2_val <= ERROR_PENALTY + 1:
                 term2 = ERROR_PENALTY
            else:
                 term2 = coeff_term2 * integral2_val
        except Exception as e:
            # print(f"WARN: quad failed in phi4 term2: {e}")
            term2 = ERROR_PENALTY
    # Early exit
    # Important: If coeff_term2 <= EPS, term2 remains 0.0. Need to check if that's ok.
    # Original code implies if coeff_term2=0, term2=0, which avoids the penalty check. Correct.
    if term2 <= ERROR_PENALTY + 1: return ERROR_PENALTY # Check only if calculated


    result = term1 + term2
    return result if np.isfinite(result) else ERROR_PENALTY


# --- phi5_new (using analytical integral A and Numba integrand B) ---
def phi5_new(p_0, p_1, p_2, p_3, tau, O, m, theta):
    if m < M_MIN_phi_other: return ERROR_PENALTY
    p_0_eff = np.clip(p_0, 0.0, 1.0)
    p_1_eff = np.clip(p_1, 0.0, 1.0)
    p_2_eff = np.clip(p_2, 0.0, 1.0)
    coeff1 = 1.0 - p_0_eff - p_1_eff # Calculate once
    if coeff1 < -EPS: return ERROR_PENALTY
    coeff1 = max(0.0, coeff1)
    if not (EPS <= tau <= 1.0 - EPS): return ERROR_PENALTY # Check tau range

    term1 = 0.0
    if coeff1 > EPS:
        integralA = 0.0 # Value if coeffA is 0
        coeffA = 1.0 - p_2_eff
        if coeffA > EPS:
            # *** Use Analytical Solution for integral A ***
            try:
                 # Check base case for pow: (1-tau) must be >= 0
                 base = 1.0 - tau
                 if base < 0: # Should not happen given tau <= 1-EPS
                      integralA_val = ERROR_PENALTY
                 elif m < 1 : # Should not happen given M_MIN_phi_other=1
                      integralA_val = ERROR_PENALTY
                 # elif m == 1: # Covered by the formula
                 #      integralA_val = base
                 else:
                     # Use math.pow for scalars
                     pow_val = math.pow(base, m)
                     if m < EPS: # Avoid division by zero if m somehow is 0
                         integralA_val = ERROR_PENALTY
                     else:
                         integralA_val = pow_val / m

                 if not np.isfinite(integralA_val): integralA_val = ERROR_PENALTY
                 # Check before multiply
                 if integralA_val <= ERROR_PENALTY + 1:
                     integralA = ERROR_PENALTY # Propagate penalty
                 else:
                     integralA = coeffA * integralA_val

            except Exception:
                 integralA = ERROR_PENALTY # Catch math errors
        # Early exit if integralA fails
        if integralA <= ERROR_PENALTY + 1: return ERROR_PENALTY

        # Calculate Integral B using Numba integrand
        integralB = 0.0
        try:
            integralB_val, integ_err = quad(
                 integrand_phi5_B_numba, tau, 1.0,
                 args=(tau, m),
                 epsabs=1e-6, epsrel=1e-6, limit=100
            )
            if not np.isfinite(integralB_val) or abs(integ_err) > 1e-2:
                 integralB_val = ERROR_PENALTY
            integralB = integralB_val # Assign penalty or value
        except Exception as e:
            # print(f"WARN: quad failed in phi5 integralB: {e}")
            integralB = ERROR_PENALTY
        # Early exit if integralB fails
        if integralB <= ERROR_PENALTY + 1: return ERROR_PENALTY

        # Combine integrals A and B for term1
        term1_combined_integral = integralA + integralB
        # Check combined value before multiplying by coeff1
        if not np.isfinite(term1_combined_integral) or term1_combined_integral <= ERROR_PENALTY + 1 :
             term1 = ERROR_PENALTY
        else:
             term1 = coeff1 * term1_combined_integral
        # Check final term1 value
        if not np.isfinite(term1): term1 = ERROR_PENALTY

    # Early exit if term1 fails (or if coeff1 was 0)
    if term1 <= ERROR_PENALTY + 1: return ERROR_PENALTY

    # --- Term 2 ---
    term2 = 0.0
    if p_0_eff > EPS:
         # Original check: if tau < EPS or tau >= 1.0 - EPS: term2 = 0.0
         # Let's keep this check for consistency, though math might handle it.
         if tau < EPS or tau >= 1.0 - EPS:
             term2 = 0.0 # Explicitly zero as per original logic
         else:
             try:
                 const_base = 1.0 - tau
                 const_pow = 0.0
                 # Check base for pow
                 if const_base > 0:
                     # Use math.pow for scalar exponentiation
                     # Handle m=1 case: pow should be (m-1) -> 0. base^0 = 1.
                     if m - 1 < EPS: # If m is 1 or less
                          const_pow = 1.0
                     else:
                          const_pow = math.pow(const_base, m - 1)
                 # else: const_pow remains 0.0 (if base is <= 0)

                 if not np.isfinite(const_pow): const_pow = 0.0 # Or error? Original had 0.0

                 const_factor = 1.0 - const_pow

                 # Calculate base integral C part (tau * log(1/tau))
                 # Check tau before log
                 if tau < EPS: # Should have been caught by earlier check
                     integralC_base = ERROR_PENALTY
                 else:
                     log_val = math.log(1.0 / tau)
                     integralC_base = tau * log_val

                 if not np.isfinite(integralC_base) or integralC_base <= ERROR_PENALTY + 1:
                     term2 = ERROR_PENALTY # Base integral invalid
                 else:
                     # Now combine factor and integral
                     term2 = p_0_eff * const_factor * integralC_base
                     if not np.isfinite(term2): term2 = ERROR_PENALTY # Check final result

             except Exception:
                 term2 = ERROR_PENALTY # Catch math errors

    # Early exit if term2 fails
    if term2 <= ERROR_PENALTY + 1: return ERROR_PENALTY

    result = term1 + term2 # Note: term1 could be 0 if coeff1 was 0
    return result if np.isfinite(result) else ERROR_PENALTY

# --- phi6_new (using Numba integrands) ---
def phi6_new(p_0, p_1, p_2, p_3, tau, O, m, theta):
    if m < M_MIN_phi_other: return ERROR_PENALTY
    p_0_eff = np.clip(p_0, 0.0, 1.0)
    p_1_eff = np.clip(p_1, 0.0, 1.0)
    p_2_eff = np.clip(p_2, 0.0, 1.0)
    coeff3 = 1.0 - p_0_eff - p_1_eff # Calculate once
    if coeff3 < -EPS: return ERROR_PENALTY
    coeff3 = max(0.0, coeff3)
    if not (EPS <= tau <= 1.0 - EPS): return ERROR_PENALTY # Check tau range

    # --- Term 1 (p_0 term) ---
    term1 = 0.0
    if p_0_eff > EPS:
        dbl_integral_val = 0.0
        try:
            # Use Numba compiled inner function for dblquad
            dbl_integral_res, dbl_int_err = dblquad(
                phi6_inner_integrand_numba, tau, 1.0, # t_star limits
                lambda t_star: 0.0, lambda t_star: tau, # tm limits
                args=(tau, m), # Args for the inner function
                epsabs=1e-5, epsrel=1e-5) # Note: dblquad default limit is 50

            if not np.isfinite(dbl_integral_res) or abs(dbl_int_err) > 1e-2:
                dbl_integral_val = ERROR_PENALTY
            else:
                dbl_integral_val = dbl_integral_res
        except Exception as e:
            # print(f"WARN: dblquad failed in phi6: {e}")
            dbl_integral_val = ERROR_PENALTY
        # Early exit check for dbl_integral part
        if dbl_integral_val <= ERROR_PENALTY + 1: return ERROR_PENALTY

        # Calculate Term B part
        termB = 0.0
        try:
            base = max(0.0, 1.0 - tau) # Ensure base is non-negative
            # Use math.pow for scalar exponentiation
            one_minus_tau_m = math.pow(base, m)
            termB = one_minus_tau_m * THETA_FACTOR
            if not np.isfinite(termB): termB = ERROR_PENALTY
        except Exception:
            termB = ERROR_PENALTY
        # Early exit check for termB part
        if termB <= ERROR_PENALTY + 1: return ERROR_PENALTY

        # Combine for term1
        term1 = p_0_eff * (dbl_integral_val + termB)
        if not np.isfinite(term1): term1 = ERROR_PENALTY # Final check on term1
    # Early exit if term1 failed (or if p_0_eff was 0)
    if term1 <= ERROR_PENALTY + 1: return ERROR_PENALTY

    # --- Term 2 (p_1 term) ---
    # This term is simple, less likely to fail unless THETA_FACTOR is weird
    term2 = p_1_eff * THETA_FACTOR
    if not np.isfinite(term2): term2 = ERROR_PENALTY # Check it anyway
    # Early exit - should we? Original didn't have early exit here.
    # Let's assume term2 is likely okay if p1 and THETA_FACTOR are okay.
    # Add check just in case:
    if term2 <= ERROR_PENALTY + 1: return ERROR_PENALTY


    # --- Term 3 (coeff3 term) ---
    term3 = 0.0
    if coeff3 > EPS:
        integralD_combined = 0.0
        try:
            # Use Numba compiled integrand for quad
            integralD_val, integ_err = quad(
                 integrand_phi6_D_combined_numba, tau, 1.0,
                 args=(tau, m, p_2_eff, THETA_FACTOR), # Pass needed args
                 epsabs=1e-6, epsrel=1e-6, limit=100
            )
            if not np.isfinite(integralD_val) or abs(integ_err) > 1e-2:
                 integralD_val = ERROR_PENALTY
            integralD_combined = integralD_val # Assign penalty or value
        except Exception as e:
            # print(f"WARN: quad failed in phi6 term3: {e}")
            integralD_combined = ERROR_PENALTY
        # Early exit if integralD fails
        if integralD_combined <= ERROR_PENALTY + 1: return ERROR_PENALTY

        # Combine for term3
        term3 = coeff3 * integralD_combined
        if not np.isfinite(term3): term3 = ERROR_PENALTY # Final check on term3
    # Early exit if term3 fails (or if coeff3 was 0)
    if term3 <= ERROR_PENALTY + 1: return ERROR_PENALTY

    result = term1 + term2 + term3
    return result if np.isfinite(result) else ERROR_PENALTY


# --- Objective Function (Considers 6 phis) ---
# Minor optimization: pass theta_factor directly if needed, but it's global
# Use math.inf instead of np.inf for potential Numba compatibility later? Stick to np for now.
def objective_function(params, m_values_phi2, m_values_other, theta):
    # Unpack parameters
    p_0, p_1, p_2, p_3, tau, O = params

    # Bounds checking (can be slightly simplified)
    if not (0.0 <= p_0 <= 1.0 + EPS and
            0.0 <= p_1 <= 1.0 + EPS and
            # Check sum constraint early
            p_0 + p_1 <= 1.0 + EPS and
            0.0 <= p_2 <= 1.0 + EPS and
            0.0 <= p_3 <= 1.0 + EPS and
            # Check tau range
            EPS <= tau <= 1.0 - 2*EPS and # Original tau upper bound
            # Check O range relative to tau
            tau + EPS < O <= 1.0 - EPS): # Original O range
        return -ERROR_PENALTY # Return the positive penalty

    # Use precalculated theta_factor if needed by phis (passed implicitly via global)

    min_phi_values_across_m = [np.inf] * 6 # Use np.inf for consistency
    # List phi functions (ensure they match the updated names)
    phi_functions = [phi1_new, phi2_new, phi3_new, phi4_new, phi5_new, phi6_new]
    m_sets = [M_VALUES_other, M_VALUES_phi2, M_VALUES_other, M_VALUES_other, M_VALUES_other, M_VALUES_other] # Use global M_VALUES lists

    min_overall = np.inf # Track the minimum across all phis and m

    # Loop through each phi function
    for i in range(6):
        current_phi_i_min = np.inf # Min value for this phi_i across all its m
        m_values_to_check = m_sets[i]

        # Loop through relevant m values for this phi
        for m in m_values_to_check:
            # Call the specific phi function
            # Pass theta explicitly as it might be needed if THETA_FACTOR wasn't global
            value = phi_functions[i](p_0, p_1, p_2, p_3, tau, O, m, theta)

            # Update the minimum for this specific phi_i
            current_phi_i_min = min(current_phi_i_min, value)

            # If any m calculation resulted in penalty, this phi_i is penalized
            if current_phi_i_min <= ERROR_PENALTY + 1:
                # No need to check further m for this phi_i
                break # Exit inner m loop

        # Store the minimum found for phi_i (could be ERROR_PENALTY)
        min_phi_values_across_m[i] = current_phi_i_min

        # Update the overall minimum across all phi functions examined so far
        min_overall = min(min_overall, current_phi_i_min)

        # If we already hit the error penalty, no need to check other phi functions
        if min_overall <= ERROR_PENALTY + 1:
            return -ERROR_PENALTY # Return positive penalty

    # If loop completes without hitting penalty, return the negative minimum
    # (since differential_evolution minimizes)
    return -min_overall


# --- Optimization Setup (6 variables) ---
bounds = [
    (0.0, 1.0),       # p_0
    (0.0, 1.0),       # p_1
    (0.0, 1.0),       # p_2
    (0.0, 1.0),       # p_3
    (EPS, 1.0 - 2*EPS),# tau
    (EPS, 1.0 - EPS)  # O - Note: constraint O > tau is handled inside objective
]

# --- Run the optimization ---
print(f"Optimizing for theta = {THETA}, m = 1..{M_MAX_INT} (phi2: {M_MIN_phi2}..{M_MAX_INT})")
print(f"Using Numba-compiled integrands and analytical integrals where possible.")
print(f"Hyperparameters: p_0, p_1, p_2, p_3, tau, O")
print("Objective: Maximize min(min_m phi1_new, ..., min_m phi6_new)")
print("Running differential_evolution...")

start_time = time.time()

# Run optimization (ensure args match objective_function)
result = differential_evolution(
    objective_function,
    bounds,
    args=(M_VALUES_phi2, M_VALUES_other, THETA), # Pass M values and theta
    strategy='best1bin',
    maxiter=1000,
    popsize=40,
    tol=1e-5,
    mutation=(0.5, 1.5),
    recombination=0.7,
    seed=42,
    disp=True,
    updating='deferred',
    workers=-1 # Use parallel processing
)

end_time = time.time()
print(f"\nOptimization finished in {end_time - start_time:.2f} seconds.")


# --- Print Results (Updated for 6 variables and 6 phis) ---
# (Keep the results printing and verification section as is, it should work
# with the optimized objective function)
if result.success:
    print("\nOptimization successful!")
    optimal_params = result.x
    # Clean and validate parameters (same as before)
    optimal_p0 = np.clip(optimal_params[0], 0.0, 1.0)
    optimal_p1 = np.clip(optimal_params[1], 0.0, 1.0)
    if optimal_p0 + optimal_p1 > 1.0:
        scale = 1.0 / (optimal_p0 + optimal_p1 + EPS) # Add EPS for safety
        optimal_p0 *= scale; optimal_p1 *= scale
        print("Note: p0 and p1 scaled down slightly to meet p0+p1 <= 1 constraint.")
        # Re-clip after scaling just in case of floating point issues
        optimal_p0 = np.clip(optimal_p0, 0.0, 1.0); optimal_p1 = np.clip(optimal_p1, 0.0, 1.0)

    optimal_p2 = np.clip(optimal_params[2], 0.0, 1.0)
    optimal_p3 = np.clip(optimal_params[3], 0.0, 1.0)
    optimal_O = np.clip(optimal_params[5], EPS, 1.0 - EPS) # Clip O first
    # Ensure tau is valid and respects O
    optimal_tau = np.clip(optimal_params[4], EPS, 1.0 - 2*EPS) # Clip tau to its bounds
    optimal_tau = min(optimal_tau, optimal_O - EPS) # Ensure tau < O
    optimal_tau = max(optimal_tau, EPS) # Ensure tau > EPS again after adjustment

    # Ensure O is valid relative to the adjusted tau
    optimal_O = max(optimal_O, optimal_tau + EPS)
    optimal_O = min(optimal_O, 1.0 - EPS) # Re-clip O to upper bound


    optimal_params_cleaned = [optimal_p0, optimal_p1, optimal_p2, optimal_p3, optimal_tau, optimal_O]

    max_min_phi_optimizer = -result.fun # Objective value from optimizer

    print(f"\nOptimal Hyperparameters (cleaned):")
    print(f"  p_0  = {optimal_params_cleaned[0]:.6f}")
    print(f"  p_1  = {optimal_params_cleaned[1]:.6f}")
    print(f"  p_0+p_1 = {optimal_params_cleaned[0]+optimal_params_cleaned[1]:.6f}") # Check sum
    print(f"  p_2  = {optimal_params_cleaned[2]:.6f}")
    print(f"  p_3  = {optimal_params_cleaned[3]:.6f}")
    print(f"  tau  = {optimal_params_cleaned[4]:.6f}")
    print(f"  O    = {optimal_params_cleaned[5]:.6f}")

    # --- Verification Step ---
    print("\nVerifying result with optimal parameters:")
    min_phi_worst_values = [np.inf] * 6
    phi_functions = [phi1_new, phi2_new, phi3_new, phi4_new, phi5_new, phi6_new]
    m_sets = [M_VALUES_other, M_VALUES_phi2, M_VALUES_other, M_VALUES_other, M_VALUES_other, M_VALUES_other]
    worst_m_for_phi = [-1] * 6
    all_phis_valid = True
    print("Calculating min_m(phi_i) for each i=1..6:")
    for i in range(6):
        current_phi_i_min = np.inf; current_worst_m = -1
        m_values_to_check = m_sets[i]
        for m in m_values_to_check:
            # Pass cleaned parameters and theta
            value = phi_functions[i](*optimal_params_cleaned, m, THETA)
            if value < current_phi_i_min:
                 current_phi_i_min = value
                 current_worst_m = m
            # Check if penalty was hit during calculation for this m
            if value <= ERROR_PENALTY + 1:
                # If any m hits penalty, the min for this phi becomes penalty
                current_phi_i_min = ERROR_PENALTY
                current_worst_m = m
                all_phis_valid = False # Mark that at least one calculation failed
                break # No need to check other m for this phi
        min_phi_worst_values[i] = current_phi_i_min
        worst_m_for_phi[i] = current_worst_m
        phi_i_min_str = f"{current_phi_i_min:.6f}" if current_phi_i_min > ERROR_PENALTY + 1 else "ERROR_PENALTY"
        print(f"  min_m phi{i+1}(m) = {phi_i_min_str} (worst m = {current_worst_m})")

    # Calculate overall min from verification step
    overall_min_check = min(min_phi_worst_values) if min_phi_worst_values else -np.inf # Handle empty list case

    if not all_phis_valid:
        print(f"\nWARNING: Some phi calculations hit penalty ({ERROR_PENALTY}).")
        print(f"Recalculated minimum objective value = {overall_min_check:.6f} (likely reflects penalty)")
    elif overall_min_check <= ERROR_PENALTY + 1:
         print(f"\nWARNING: Recalculated minimum ({overall_min_check:.6f}) is penalty value.")
    else:
         print(f"\nRecalculated objective value = min(min_m phi1, ..., min_m phi6) = {overall_min_check:.6f}")

    print(f"Optimizer objective value = {max_min_phi_optimizer:.6f}")

    # Compare optimizer result with verification calculation
    if all_phis_valid and overall_min_check > ERROR_PENALTY + 1:
        # Use a reasonable tolerance for comparison
        if not np.isclose(overall_min_check, max_min_phi_optimizer, rtol=1e-3, atol=1e-4):
             print("WARNING: Optimizer value differs significantly from verification calculation.")
        else:
             print("Verification matches optimizer value within tolerance.")
        # Find which phi limited the result
        limiting_phi_index = np.argmin(min_phi_worst_values)
        print(f"The overall minimum was determined by phi{limiting_phi_index+1} (at its worst m={worst_m_for_phi[limiting_phi_index]})")

else:
    # --- Failure reporting (same as before) ---
    print("\nOptimization failed or did not converge.")
    print(f"Message: {result.message}")
    # ... (rest of failure reporting)