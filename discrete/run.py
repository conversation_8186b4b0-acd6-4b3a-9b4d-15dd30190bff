import subprocess
import re
import sys
import os
import concurrent.futures
import time # For measuring total time
import matplotlib.pyplot as plt
import numpy as np

# --- Configuration ---
SCRIPT_TO_RUN = "discrete/hyperopt5.py"  # Make sure this script is in the same directory or in PATH
NUM_RUNS = 100  # How many times to run the optimization
# Adjust MAX_CONCURRENT_RUNS based on your CPU cores and how resource-intensive hyperopt5.py is.
# os.cpu_count() is a good starting point if hyperopt5.py is CPU-bound.
# You might increase this if hyperopt5.py has significant I/O wait times.
MAX_CONCURRENT_RUNS = 30 # Default to 4 if cpu_count() is None
RUN_TIMEOUT_SECONDS = 300 # Timeout for each individual run of hyperopt5.py
# --- End Configuration ---

def parse_optimization_output(output_text):
    """
    Parses the output of hyperopt5.py to extract key metrics.
    Returns a dictionary with extracted values, or None if crucial parts are missing.
    """
    data = {}
    try:
        ratio_match = re.search(r"Maximum Minimum Competitive Ratio:\s*([0-9.]+)", output_text)
        if ratio_match:
            data['max_min_ratio'] = float(ratio_match.group(1))
        else:
            # If this crucial line is missing, we can't proceed with this run's data
            # print("Debug: 'Maximum Minimum Competitive Ratio' not found in output.") # For debugging
            return None # Indicate parsing failure for the key metric

        params_patterns = {
            "tau": r"Optimal tau:\s*([0-9.]+)",
            "p0": r"Optimal p0:\s*([0-9.]+)",
            "p1": r"Optimal p1:\s*([0-9.]+)",
            "p3": r"Optimal p3:\s*([0-9.]+)",
            "p4": r"Optimal p4:\s*([0-9.]+)",
            "prob_t1": r"Prob T=1:\s*([0-9.]+)",
        }

        for key, pattern in params_patterns.items():
            match = re.search(pattern, output_text)
            if match:
                data[key] = float(match.group(1))
            # else:
                # print(f"Debug: Pattern for '{key}' not found in output.") # For debugging

        return data
    except Exception as e:
        print(f"Error during parsing: {e}\nOutput was:\n{output_text[:500]}...") # Print start of output for context
        return None

def extract_case_ratios_for_plotting(output_text):
    """
    Extracts competitive ratios for each case from the hyperopt5.py output.
    This is used for plotting the adversarial m values.
    Returns a dictionary with case names and their ratios.
    """
    case_ratios = {}
    try:
        # Extract individual case ratios from the output
        case_patterns = {
            "Case 1/2": r"Case 1/2:\s*([0-9.]+)",
            "Case 3": r"Case 3:\s*([0-9.]+)",
            "Case 4": r"Case 4:\s*([0-9.]+)",
            "Case 5": r"Case 5:\s*([0-9.]+)",
            "Case 6": r"Case 6:\s*([0-9.]+)",
        }

        for case_name, pattern in case_patterns.items():
            match = re.search(pattern, output_text)
            if match:
                case_ratios[case_name] = float(match.group(1))

        return case_ratios
    except Exception as e:
        print(f"Error extracting case ratios: {e}")
        return {}

def create_m_analysis_script(best_params):
    """
    Creates a script that analyzes competitive ratios for different m values
    using the optimal parameters found.
    """
    script_content = f'''
import sys
sys.path.append('discrete')
from hyperopt5 import *

# Optimal parameters from best run
tau_opt = {best_params['tau']:.10f}
p0_opt = {best_params['p0']:.10f}
p1_opt = {best_params['p1']:.10f}
p3_opt = {best_params['p3']:.10f}
p4_opt = {best_params['p4']:.10f}

print("M_ANALYSIS_START")
print("m,case3,case4,case5,case6,min_ratio")

# Calculate competitive ratios for each m value
for m in range(1, 51):
    # Case 1/2 doesn't depend on m
    case12 = calculate_case1_2(tau_opt, p0_opt, p1_opt, p3_opt, p4_opt)

    # Case 3 (only for m >= 2)
    if m >= 2:
        case3 = calculate_case3(tau_opt, p0_opt, p1_opt, p3_opt, p4_opt, m)
    else:
        case3 = float('inf')

    # Case 4 (revised)
    case4 = calculate_case4_revised(tau_opt, p0_opt, p1_opt, p3_opt, p4_opt, m)

    # Case 5
    case5 = calculate_case5(tau_opt, p0_opt, p1_opt, p3_opt, p4_opt, m)

    # Case 6
    case6 = calculate_case6(tau_opt, p0_opt, p1_opt, p3_opt, p4_opt, m)

    # Find minimum (worst case)
    valid_cases = [case12, case3, case4, case5, case6]
    valid_cases = [c for c in valid_cases if np.isfinite(c)]
    min_ratio = min(valid_cases) if valid_cases else float('inf')

    print(f"{{m}},{{case3:.6f}},{{case4:.6f}},{{case5:.6f}},{{case6:.6f}},{{min_ratio:.6f}}")

print("M_ANALYSIS_END")
'''
    return script_content

def plot_m_analysis(best_params, best_iteration_num):
    """
    Creates and runs an analysis script to plot competitive ratios vs m values
    for the optimal parameters.
    """
    print(f"\n--- Creating M-value Analysis Plot for Best Run (Iteration {best_iteration_num}) ---")

    # Create the analysis script
    script_content = create_m_analysis_script(best_params)

    # Write the script to a temporary file
    temp_script_path = "temp_m_analysis.py"
    try:
        with open(temp_script_path, 'w') as f:
            f.write(script_content)

        # Run the analysis script
        result = subprocess.run(
            [sys.executable, temp_script_path],
            capture_output=True,
            text=True,
            timeout=60
        )

        if result.returncode == 0:
            # Parse the output
            lines = result.stdout.strip().split('\n')
            start_idx = -1
            end_idx = -1

            for i, line in enumerate(lines):
                if line.strip() == "M_ANALYSIS_START":
                    start_idx = i + 2  # Skip header line
                elif line.strip() == "M_ANALYSIS_END":
                    end_idx = i
                    break

            if start_idx != -1 and end_idx != -1:
                # Extract data
                m_values = []
                case3_values = []
                case4_values = []
                case5_values = []
                case6_values = []
                min_values = []

                for line in lines[start_idx:end_idx]:
                    parts = line.split(',')
                    if len(parts) == 6:
                        m_values.append(int(parts[0]))
                        case3_values.append(float(parts[1]) if parts[1] != 'inf' else None)
                        case4_values.append(float(parts[2]) if parts[2] != 'inf' else None)
                        case5_values.append(float(parts[3]) if parts[3] != 'inf' else None)
                        case6_values.append(float(parts[4]) if parts[4] != 'inf' else None)
                        min_values.append(float(parts[5]) if parts[5] != 'inf' else None)

                # Create the plot
                plt.figure(figsize=(12, 8))

                # Plot each case
                plt.plot(m_values, case3_values, 'o-', label='Case 3', alpha=0.7)
                plt.plot(m_values, case4_values, 's-', label='Case 4', alpha=0.7)
                plt.plot(m_values, case5_values, '^-', label='Case 5', alpha=0.7)
                plt.plot(m_values, case6_values, 'd-', label='Case 6', alpha=0.7)
                plt.plot(m_values, min_values, 'k-', linewidth=2, label='Minimum (Adversarial)', alpha=0.9)

                plt.xlabel('m (Adversarial Parameter)', fontsize=12)
                plt.ylabel('Competitive Ratio', fontsize=12)
                plt.title(f'Competitive Ratio vs Adversarial Parameter m', fontsize=14)
                plt.legend()
                plt.grid(True, alpha=0.3)
                plt.tight_layout()

                # Save the plot
                plot_filename = f"m_analysis_plot_iteration_{best_iteration_num}.png"
                plt.savefig(plot_filename, dpi=300, bbox_inches='tight')
                print(f"Plot saved as: {plot_filename}")

                # Show the plot
                plt.show()

                # Print some statistics
                valid_mins = [v for v in min_values if v is not None]
                if valid_mins:
                    worst_m = m_values[min_values.index(min(valid_mins))]
                    print(f"Worst-case m value: {worst_m} (ratio: {min(valid_mins):.6f})")
                    print(f"Best-case m value: {m_values[min_values.index(max(valid_mins))]} (ratio: {max(valid_mins):.6f})")

            else:
                print("Could not parse analysis output properly.")
                print("Raw output:")
                print(result.stdout)
        else:
            print(f"Analysis script failed with return code {result.returncode}")
            print("Error output:")
            print(result.stderr)

    except Exception as e:
        print(f"Error during m-value analysis: {e}")
    finally:
        # Clean up temporary file
        if os.path.exists(temp_script_path):
            os.remove(temp_script_path)

def run_single_optimization(iteration_num, script_path, timeout):
    """
    Runs a single instance of the optimization script and parses its output.
    Returns a dictionary containing the iteration number, parsed data, full output, and any error.
    """
    # print(f"Thread for iteration {iteration_num}: Starting run...")
    result = {
        "iteration": iteration_num,
        "parsed_data": None,
        "full_output": "",
        "error": None
    }
    try:
        process = subprocess.run(
            [sys.executable, script_path],
            capture_output=True,
            text=True,
            check=False, # We'll check returncode manually to get stderr even on failure
            timeout=timeout
        )
        result["full_output"] = process.stdout
        if process.stderr: # Include stderr if any
            result["full_output"] += "\n--- STDERR ---\n" + process.stderr

        if process.returncode != 0:
            result["error"] = f"Script returned non-zero exit code: {process.returncode}"
            # print(f"Thread for iteration {iteration_num}: Script error (code {process.returncode}).")
            # Still try to parse, as some output might be present
            
        parsed_data = parse_optimization_output(process.stdout) # Only parse stdout
        if parsed_data and 'max_min_ratio' in parsed_data:
            result["parsed_data"] = parsed_data
            # print(f"Thread for iteration {iteration_num}: Parsed ratio {parsed_data['max_min_ratio']:.6f}")
        elif not result["error"]: # If no script error, but parsing failed
            result["error"] = "Could not parse competitive ratio from script output."
            # print(f"Thread for iteration {iteration_num}: Parsing error.")


    except subprocess.TimeoutExpired as e:
        result["error"] = f"Script timed out after {timeout} seconds."
        result["full_output"] = e.stdout if e.stdout else ""
        if e.stderr: result["full_output"] += "\n--- STDERR ---\n" + e.stderr
        # print(f"Thread for iteration {iteration_num}: Timeout.")
    except FileNotFoundError:
        result["error"] = f"Script '{script_path}' not found."
        # print(f"Thread for iteration {iteration_num}: File not found.")
    except Exception as e:
        result["error"] = f"An unexpected error occurred: {str(e)}"
        # print(f"Thread for iteration {iteration_num}: Unexpected error {str(e)}.")
    
    return result

def main():
    best_overall_ratio = -float('inf')
    best_run_details = None
    best_run_full_output = ""
    best_iteration_num = -1
    
    # Check if script exists once before starting
    if not os.path.exists(SCRIPT_TO_RUN):
        print(f"Error: Optimization script '{SCRIPT_TO_RUN}' not found at the specified path.")
        print("Please ensure it's in the current directory or provide the correct path.")
        return

    print(f"Starting {NUM_RUNS} optimization runs for '{SCRIPT_TO_RUN}' using up to {MAX_CONCURRENT_RUNS} concurrent threads...\n")
    start_time = time.time()

    futures = []
    results_from_runs = []

    with concurrent.futures.ThreadPoolExecutor(max_workers=MAX_CONCURRENT_RUNS) as executor:
        for i in range(NUM_RUNS):
            # print(f"Submitting task for iteration {i + 1}...") # Verbose
            futures.append(executor.submit(run_single_optimization, i + 1, SCRIPT_TO_RUN, RUN_TIMEOUT_SECONDS))

        # Process results as they complete
        for i, future in enumerate(concurrent.futures.as_completed(futures)):
            try:
                run_result = future.result()
                results_from_runs.append(run_result) # Store all results for potential later analysis

                iteration_num = run_result["iteration"]
                parsed_data = run_result["parsed_data"]
                error_msg = run_result["error"]

                progress_percent = ((i + 1) / NUM_RUNS) * 100
                sys.stdout.write(f"\rProgress: {i+1}/{NUM_RUNS} runs completed ({progress_percent:.1f}%). ")
                sys.stdout.flush()


                if error_msg:
                    # Log errors if needed, but don't interrupt the main comparison flow
                    # print(f"\nNote: Iteration {iteration_num} had an error: {error_msg}")
                    pass # Error already stored in run_result

                if parsed_data and 'max_min_ratio' in parsed_data:
                    current_ratio = parsed_data['max_min_ratio']
                    # print(f"Iteration {iteration_num} completed. Ratio: {current_ratio:.6f}") # Can be too verbose
                    if current_ratio > best_overall_ratio:
                        best_overall_ratio = current_ratio
                        best_run_details = parsed_data
                        best_run_full_output = run_result["full_output"]
                        best_iteration_num = iteration_num
                        # print(f"\n*** New best ratio from iteration {iteration_num}: {best_overall_ratio:.6f} ***")
                # else if not error_msg: # No error, but also no ratio
                    # print(f"\nNote: Iteration {iteration_num} completed but 'max_min_ratio' not found in parsed data.")


            except Exception as e: # Should ideally be caught within run_single_optimization or future.result() itself
                print(f"\nCritical error processing future for an iteration: {e}")
                results_from_runs.append({
                    "iteration": "unknown", "parsed_data": None, "full_output": "",
                    "error": f"Future processing error: {str(e)}"
                })
    
    print("\n\nAll runs completed.")
    end_time = time.time()
    print(f"Total execution time: {end_time - start_time:.2f} seconds.")

    print("\n======== MULTI-RUN OPTIMIZATION SUMMARY ========")
    if best_run_details:
        print(f"Highest 'Maximum Minimum Competitive Ratio' found: {best_overall_ratio:.6f}")
        print(f"(Achieved in Iteration {best_iteration_num} out of {NUM_RUNS})")
        print("Parameters for the best run:")
        for key, value in best_run_details.items():
            if key != 'max_min_ratio':
                print(f"  Optimal {key.replace('_', ' ').capitalize()}: {value:.6f}")

        print(f"\n--- Full output of the BEST run (Iteration {best_iteration_num}) ---")
        print(best_run_full_output)

        # Create the m-value analysis plot
        plot_m_analysis(best_run_details, best_iteration_num)

    else:
        print("No successful runs were able to produce a 'Maximum Minimum Competitive Ratio'.")
        print("Review individual run errors if any were reported or check parsing logic.")

    # Optional: Print summary of errors if any occurred
    errors_found = [res for res in results_from_runs if res.get("error")]
    if errors_found:
        print(f"\n--- Summary of Runs with Errors ({len(errors_found)}/{NUM_RUNS}) ---")
        for res in errors_found:
            print(f"Iteration {res['iteration']}: {res['error']}")
            if "non-zero exit code" in res['error'] or "timed out" in res['error']:
                 # print first few lines of output for context on script errors
                 output_preview = res['full_output'].strip().split('\n')
                 print(f"  Output Preview: {' | '.join(output_preview[:3])}...")


if __name__ == "__main__":
    main()