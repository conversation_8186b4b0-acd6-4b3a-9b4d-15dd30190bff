import numpy as np
from scipy.integrate import quad, dblquad
from scipy.optimize import differential_evolution
import warnings
import math

# --- Constants and Setup ---
THETA = 0.6
EPS = 1e-9
M_VALUES = list(range(1, 21))

# --- phi function definitions (Assume they are correctly defined as in previous steps) ---
# [Insert the full, robust definitions of phi1, phi2, phi3, phi4, phi5 here,
#  ensuring they return -1e18 on errors/invalid inputs/failed integration]
# --- Start Placeholder for Phi Functions ---
def phi1(p, O, p_2, tau, m, theta):
    if O < EPS: return -1e18
    p_eff = np.clip(p, 0, 1)
    val = (1 - p_eff) * tau / max(O, EPS)
    return val if np.isfinite(val) else -1e18

def phi2(p, O, p_2, tau, m, theta):
    p_eff = np.clip(p, 0, 1)
    p_2_eff = np.clip(p_2, 0, 1)
    if not (EPS <= tau < O < 1.0 - EPS): return -1e18 # Check bounds early

    if tau < EPS: term1 = -1e18
    else:
        try:
            log_val = np.log(1.0 / max(tau, EPS))
            term1 = p_eff * (tau * log_val)
            if not np.isfinite(term1): term1 = -1e18
        except Exception: term1 = -1e18

    def integrand(t):
        base = 1.0 - t
        t_safe = max(t, EPS)
        if m == 1 and abs(base) < EPS: return -1e17 # Divergence at t=1
        if base < 0: return -1e18
        base = max(0, base)

        if m == 1:
            factor1 = 1.0 / base
            term_a = (1.0 - factor1) * (tau / t_safe)
            term_b = factor1 * (1.0 - p_2_eff)
        elif m == 2:
            term_a = 0.0
            term_b = 1.0 * (1.0 - p_2_eff)
        else: # m > 2
            if abs(base) < EPS: factor1 = 0.0
            else: factor1 = base**(m - 2)
            term_a = (1.0 - factor1) * (tau / t_safe)
            term_b = factor1 * (1.0 - p_2_eff)

        res = term_a + term_b
        return res if np.isfinite(res) else -1e18

    try:
        integral_val, integ_err = quad(integrand, tau, O, epsabs=1e-6, epsrel=1e-6, limit=100)
        if not np.isfinite(integral_val) or abs(integ_err) > 1e-2:
            return -1e18
        term2 = (1.0 - p_eff) * integral_val
        if m == 2: # Use analytical for m=2
             term2 = (1.0 - p_eff) * (1.0 - p_2_eff) * (O - tau)
    except Exception as e:
        return -1e18

    result = term1 + term2
    return result if np.isfinite(result) else -1e18

def phi3(p, O, p_2, tau, m, theta):
    p_eff = np.clip(p, 0, 1)
    denom_theta = 1.0 + theta
    if abs(denom_theta) < EPS: return -1e18
    theta_factor = (1.0 - theta) / denom_theta

    def integrand(t):
        t_safe = max(t, EPS)
        val = (tau / t_safe) + ((t_safe - tau) / t_safe) * theta_factor
        return val if np.isfinite(val) else 0.0

    if tau >= 1.0 - EPS or tau < EPS: return 0.0

    try:
         integral_val, integ_err = quad(integrand, tau, 1.0, epsabs=1e-6, epsrel=1e-6)
         if not np.isfinite(integral_val) or abs(integ_err) > 1e-3:
             return -1e18
    except Exception as e:
        return -1e18

    result = p_eff * integral_val
    return result if np.isfinite(result) else -1e18

def phi4(p, O, p_2, tau, m, theta):
    p_eff = np.clip(p, 0, 1)
    if m < 1: return -1e18
    if not (EPS <= tau < O <= 1.0 - EPS): return -1e18

    # Integral A
    one_minus_tau = max(0, 1.0 - tau)
    one_minus_O = max(0, 1.0 - O)
    try:
        if m == 0: integralA = -1e18
        else: integralA = (one_minus_tau**m - one_minus_O**m) / m
    except Exception: integralA = -1e18
    if not np.isfinite(integralA): integralA = -1e18

    # Integral B
    def integrandB(t):
         base = 1.0 - t
         t_safe = max(t, EPS)
         if base < EPS: return 0.0 # Should not happen if O < 1-EPS

         if m == 1: factor = 1.0
         else: factor = base**(m - 1)
         if not np.isfinite(factor): factor = 0

         integrand_val = (1.0 - factor) * (tau / t_safe)
         return integrand_val if np.isfinite(integrand_val) else 0.0

    try:
        integralB, integ_errB = quad(integrandB, tau, O, epsabs=1e-6, epsrel=1e-6, limit=100)
        if not np.isfinite(integralB) or abs(integ_errB) > 1e-3: integralB = -1e18
    except Exception: integralB = -1e18

    part1_combined_integral = integralA + integralB if np.isfinite(integralA) and np.isfinite(integralB) else -1e18
    term1 = (1.0 - p_eff) * part1_combined_integral if np.isfinite(part1_combined_integral) else -1e18

    # Integral C
    if tau < EPS or tau >= 1.0 - EPS: integralC_analytic = 0.0
    else:
        const_factor_base = 1.0 - tau
        if m == 1: const_factor_pow = 1.0
        else: const_factor_pow = const_factor_base**(m - 1)
        const_factor = 1.0 - const_factor_pow
        try: log_val = np.log(1.0 / tau)
        except Exception: log_val = -np.inf
        integralC_analytic = const_factor * tau * log_val
        if not np.isfinite(integralC_analytic): integralC_analytic = -1e18

    term2 = p_eff * integralC_analytic if np.isfinite(integralC_analytic) else -1e18

    result = term1 + term2
    return result if np.isfinite(result) else -1e18

def phi5(p, O, p_2, tau, m, theta):
    p_eff = np.clip(p, 0, 1)
    p_2_eff = np.clip(p_2, 0, 1)
    if not (EPS <= tau < O < 1.0 - EPS): return -1e18 # Check bounds early

    denom_theta = 1.0 + theta
    if abs(denom_theta) < EPS: return -1e18
    theta_factor = (1.0 - theta) / denom_theta

    # Part 1: p * (...)
    def nested_integrand(tm, t_star):
        t_star_safe = max(t_star, tau, EPS)
        tm_safe = np.clip(tm, 0, tau)
        denom = t_star_safe - tm_safe
        if abs(denom) < EPS:
            num = tau - tm_safe
            if abs(num) < EPS: return 0.0
            else: return np.sign(num) * 1e17 # Avoid inf

        base_tm = max(0, 1.0 - tm_safe)
        if m == 1: pow_tm = 1.0
        elif m < 1: return 0.0
        else: pow_tm = base_tm**(m - 1)
        if not np.isfinite(pow_tm): pow_tm = 0.0

        if m < 1: return 0.0
        fraction = (tau - tm_safe) / denom
        result = m * pow_tm * fraction
        if not np.isfinite(result): return np.sign(result) * 1e17 if result != 0 else 0.0
        return result

    nested_integral_val = 0.0
    if tau >= 1.0 - EPS or tau < EPS: nested_integral_val = 0.0
    else:
        try:
             nested_integral_val, nested_integ_err = dblquad(
                 nested_integrand, tau, 1.0, lambda t_star: 0.0, lambda t_star: tau,
                 epsabs=1e-5, epsrel=1e-5)
             if not np.isfinite(nested_integral_val) or abs(nested_integ_err) > 1e-2:
                 nested_integral_val = -1e18
        except Exception as e: nested_integral_val = -1e18

    one_minus_tau_m = (max(0, 1.0 - tau))**m
    term1_other = one_minus_tau_m * theta_factor
    if not np.isfinite(term1_other): term1_other = -1e18

    part1 = p_eff * (nested_integral_val + term1_other) if np.isfinite(nested_integral_val) and np.isfinite(term1_other) else -1e18

    # Part 2: (1-p) * (...)
    def integrand_part2(t):
        base = 1.0 - t
        t_safe = max(t, EPS)
        if base < EPS: return 0.0 # Should not happen O < 1-EPS

        pow_t_m = base**m
        if not np.isfinite(pow_t_m): pow_t_m = 0.0

        term_a_factor = 1.0 - pow_t_m
        term_a = term_a_factor * (tau / t_safe)
        term_b = term_a_factor * p_2_eff * ((t_safe - tau) / t_safe) * theta_factor
        term_c = pow_t_m * theta_factor
        res = term_a + term_b + term_c
        return res if np.isfinite(res) else 0.0

    integral_part2 = 0.0
    # O > tau + EPS and O < 1.0 - EPS checked at function start
    try:
        integral_part2, integ_err_part2 = quad(integrand_part2, tau, O, epsabs=1e-6, epsrel=1e-6, limit=100)
        if not np.isfinite(integral_part2) or abs(integ_err_part2) > 1e-3:
            integral_part2 = -1e18
    except Exception as e: integral_part2 = -1e18

    part2 = (1.0 - p_eff) * integral_part2 if np.isfinite(integral_part2) else -1e18

    result = part1 + part2
    return result if np.isfinite(result) else -1e18
# --- End Placeholder for Phi Functions ---


# --- Helper to calculate all phis (reused from optimization) ---
def calculate_all_phis(params, m, theta):
    p, O, p_2, tau = params
    with warnings.catch_warnings():
        warnings.simplefilter("ignore")
        # Basic check first (redundant if called from evaluate_parameters, but safe)
        if not (0.0 <= p <= 1.0 and EPS <= tau < O < 1.0 - EPS and 0.0 <= p_2 <= 1.0):
             return [-1e18] * 5
        results = [
            phi1(p, O, p_2, tau, m, theta),
            phi2(p, O, p_2, tau, m, theta),
            phi3(p, O, p_2, tau, m, theta),
            phi4(p, O, p_2, tau, m, theta),
            phi5(p, O, p_2, tau, m, theta),
            ]
        # Ensure finite, replace non-finite with penalty value
        results = [r if np.isfinite(r) else -1e18 for r in results]
    return results

# --- New Evaluation Function ---
def evaluate_parameters(p, O, p_2, tau):
    """
    Evaluates a given set of hyperparameters (p, O, p_2, tau) for all phi
    functions across m=1 to 20.

    Args:
        p (float): Hyperparameter p.
        O (float): Hyperparameter O.
        p_2 (float): Hyperparameter p_2.
        tau (float): Hyperparameter tau.

    Returns:
        tuple: (dict, float) containing:
            - results_per_m (dict): Dictionary where keys are m values and
                                    values are dicts {'all': list_of_phis, 'min': min_phi}.
            - overall_min_phi (float): The minimum phi value found across all m.
        Returns (None, -np.inf) if parameters are invalid.
    """
    print("-" * 60)
    print(f"Evaluating parameters: p={p:.4f}, O={O:.4f}, p_2={p_2:.4f}, tau={tau:.4f}")
    print(f"Using fixed THETA = {THETA}")

    # --- Validate Parameters ---
    is_valid = True
    if not (0.0 <= p <= 1.0):
        print("ERROR: p must be between 0.0 and 1.0")
        is_valid = False
    if not (0.0 <= p_2 <= 1.0):
        print("ERROR: p_2 must be between 0.0 and 1.0")
        is_valid = False
    # Check tau bounds (must be > 0 and < 1)
    if not (EPS <= tau < 1.0 - EPS):
         print(f"ERROR: tau must be between {EPS:.1e} and {1.0-EPS:.4f}")
         is_valid = False
    # Check O bounds (must be > 0 and < 1)
    if not (EPS <= O < 1.0 - EPS):
         print(f"ERROR: O must be between {EPS:.1e} and {1.0-EPS:.4f}")
         is_valid = False
    # Check tau < O relationship AFTER checking individual bounds
    if is_valid and not (tau + EPS < O):
         print(f"ERROR: tau ({tau:.4f}) must be strictly less than O ({O:.4f})")
         is_valid = False

    if not is_valid:
        print("Parameter validation failed. Cannot evaluate.")
        print("-" * 60)
        return None, -np.inf # Indicate failure

    # --- Calculate Phi Values ---
    results_per_m = {}
    overall_min_phi = np.inf
    params_list = [p, O, p_2, tau]
    any_errors = False

    print("\nCalculating phi values for m = 1 to 20...")
    # Header for the results table
    print("-" * 85)
    print(f"{'m':>3s} | {'phi1':>12s} | {'phi2':>12s} | {'phi3':>12s} | {'phi4':>12s} | {'phi5':>12s} | {'min_phi':>12s}")
    print("-" * 85)

    for m in M_VALUES:
        phi_values = calculate_all_phis(params_list, m, THETA)

        # Check if any calculation resulted in the penalty value
        if any(val <= -1e17 for val in phi_values):
            any_errors = True
            # Mark results containing errors? Add a '*' maybe
            error_marker = "*"
        else:
            error_marker = " "

        try:
            min_phi_for_m = min(phi_values)
        except ValueError: # Should not happen if calculate_all_phis returns list
            min_phi_for_m = -np.inf

        results_per_m[m] = {'all': phi_values, 'min': min_phi_for_m}
        overall_min_phi = min(overall_min_phi, min_phi_for_m)

        # Print results row, formatting numbers or showing 'ERROR'
        phi_strs = []
        for val in phi_values:
            if val <= -1e17: phi_strs.append(f"{'ERROR':>12s}")
            else: phi_strs.append(f"{val:>12.6f}")

        min_phi_str = f"{'ERROR':>12s}" if min_phi_for_m <= -1e17 else f"{min_phi_for_m:>12.6f}"

        print(f"{m:>3d}{error_marker}| {phi_strs[0]} | {phi_strs[1]} | {phi_strs[2]} | {phi_strs[3]} | {phi_strs[4]} | {min_phi_str}")

    print("-" * 85)
    if any_errors:
        print("\n(*) Note: Some phi calculations resulted in errors (marked with '*').")

    overall_min_phi_str = f"{'ERROR':>12s}" if overall_min_phi <= -1e17 else f"{overall_min_phi:.6f}"
    print(f"\nOverall Minimum Phi value across all m = {overall_min_phi_str}")
    print("-" * 60)

    return results_per_m, overall_min_phi

# --- User Input Loop ---
def run_manual_evaluation():
    while True:
        print("\nEnter hyperparameters to evaluate (or type 'quit' to exit):")
        try:
            p_str = input("Enter p (0.0 to 1.0): ")
            if p_str.lower() == 'quit': break
            p = float(p_str)

            O_str = input("Enter O (> tau, < 1.0): ")
            if O_str.lower() == 'quit': break
            O = float(O_str)

            p_2_str = input("Enter p_2 (0.0 to 1.0): ")
            if p_2_str.lower() == 'quit': break
            p_2 = float(p_2_str)

            tau_str = input("Enter tau (> 0, < O): ")
            if tau_str.lower() == 'quit': break
            tau = float(tau_str)

            # Call the evaluation function
            evaluate_parameters(p, O, p_2, tau)

        except ValueError:
            print("Invalid input. Please enter numeric values for hyperparameters.")
        except Exception as e:
            print(f"An unexpected error occurred: {e}")

        # Ask to continue
        cont = input("\nEvaluate another set? (y/n): ").lower()
        if cont != 'y':
            break

# --- Main Execution ---
if __name__ == "__main__":
    # You can choose to run the optimization OR the manual evaluation

    # Option 1: Run Optimization (Comment out if not needed)
    # print("Starting Optimization Process...")
    # bounds = [ (0.0, 1.0), (EPS, 1.0 - EPS), (0.0, 1.0), (EPS, 1.0 - 2*EPS) ]
    # result = differential_evolution( objective_function, bounds, args=(M_VALUES, THETA), # ... other args ...
    #                                  maxiter=100, popsize=15, disp=True, workers=1) # Faster settings for demo
    # # ... (print optimization results) ...


    # Option 2: Run Manual Evaluation
    run_manual_evaluation()

    print("\nExiting.")