Starting 640 optimization runs for 'discrete/hyperopt5.py' using up to 5 concurrent threads...


Progress: 1/640 runs completed (0.2%). 
Progress: 2/640 runs completed (0.3%). 
Progress: 3/640 runs completed (0.5%). 
Progress: 4/640 runs completed (0.6%). 
Progress: 5/640 runs completed (0.8%). 
Progress: 6/640 runs completed (0.9%). 
Progress: 7/640 runs completed (1.1%). 
Progress: 8/640 runs completed (1.2%). 
Progress: 9/640 runs completed (1.4%). 
Progress: 10/640 runs completed (1.6%). 
Progress: 11/640 runs completed (1.7%). 
Progress: 12/640 runs completed (1.9%). 
Progress: 13/640 runs completed (2.0%). 
Progress: 14/640 runs completed (2.2%). 
Progress: 15/640 runs completed (2.3%). 
Progress: 16/640 runs completed (2.5%). 
Progress: 17/640 runs completed (2.7%). 
Progress: 18/640 runs completed (2.8%). 
Progress: 19/640 runs completed (3.0%). 
Progress: 20/640 runs completed (3.1%). 
Progress: 21/640 runs completed (3.3%). 
Progress: 22/640 runs completed (3.4%). 
Progress: 23/640 runs completed (3.6%). 
Progress: 24/640 runs completed (3.8%). 
Progress: 25/640 runs completed (3.9%). 
Progress: 26/640 runs completed (4.1%). 
Progress: 27/640 runs completed (4.2%). 
Progress: 28/640 runs completed (4.4%). 
Progress: 29/640 runs completed (4.5%). 
Progress: 30/640 runs completed (4.7%). 
Progress: 31/640 runs completed (4.8%). 
Progress: 32/640 runs completed (5.0%). 
Progress: 33/640 runs completed (5.2%). 
Progress: 34/640 runs completed (5.3%). 
Progress: 35/640 runs completed (5.5%). 
Progress: 36/640 runs completed (5.6%). 
Progress: 37/640 runs completed (5.8%). 
Progress: 38/640 runs completed (5.9%). 
Progress: 39/640 runs completed (6.1%). 
Progress: 40/640 runs completed (6.2%). 
Progress: 41/640 runs completed (6.4%). 
Progress: 42/640 runs completed (6.6%). 
Progress: 43/640 runs completed (6.7%). 
Progress: 44/640 runs completed (6.9%). 
Progress: 45/640 runs completed (7.0%). 
Progress: 46/640 runs completed (7.2%). 
Progress: 47/640 runs completed (7.3%). 
Progress: 48/640 runs completed (7.5%). 
Progress: 49/640 runs completed (7.7%). 
Progress: 50/640 runs completed (7.8%). 
Progress: 51/640 runs completed (8.0%). 
Progress: 52/640 runs completed (8.1%). 
Progress: 53/640 runs completed (8.3%). 
Progress: 54/640 runs completed (8.4%). 
Progress: 55/640 runs completed (8.6%). 
Progress: 56/640 runs completed (8.8%). 
Progress: 57/640 runs completed (8.9%). 
Progress: 58/640 runs completed (9.1%). 
Progress: 59/640 runs completed (9.2%). 
Progress: 60/640 runs completed (9.4%). 
Progress: 61/640 runs completed (9.5%). 
Progress: 62/640 runs completed (9.7%). 
Progress: 63/640 runs completed (9.8%). 
Progress: 64/640 runs completed (10.0%). 
Progress: 65/640 runs completed (10.2%). 
Progress: 66/640 runs completed (10.3%). 
Progress: 67/640 runs completed (10.5%). 
Progress: 68/640 runs completed (10.6%). 
Progress: 69/640 runs completed (10.8%). 
Progress: 70/640 runs completed (10.9%). 
Progress: 71/640 runs completed (11.1%). 
Progress: 72/640 runs completed (11.2%). 
Progress: 73/640 runs completed (11.4%). 
Progress: 74/640 runs completed (11.6%). 
Progress: 75/640 runs completed (11.7%). 
Progress: 76/640 runs completed (11.9%). 
Progress: 77/640 runs completed (12.0%). 
Progress: 78/640 runs completed (12.2%). 
Progress: 79/640 runs completed (12.3%). 
Progress: 80/640 runs completed (12.5%). 
Progress: 81/640 runs completed (12.7%). 
Progress: 82/640 runs completed (12.8%). 
Progress: 83/640 runs completed (13.0%). 
Progress: 84/640 runs completed (13.1%). 
Progress: 85/640 runs completed (13.3%). 
Progress: 86/640 runs completed (13.4%). 
Progress: 87/640 runs completed (13.6%). 
Progress: 88/640 runs completed (13.8%). 
Progress: 89/640 runs completed (13.9%). 
Progress: 90/640 runs completed (14.1%). 
Progress: 91/640 runs completed (14.2%). 
Progress: 92/640 runs completed (14.4%). 
Progress: 93/640 runs completed (14.5%). 
Progress: 94/640 runs completed (14.7%). 
Progress: 95/640 runs completed (14.8%). 
Progress: 96/640 runs completed (15.0%). 
Progress: 97/640 runs completed (15.2%). 
Progress: 98/640 runs completed (15.3%). 
Progress: 99/640 runs completed (15.5%). 
Progress: 100/640 runs completed (15.6%). 
Progress: 101/640 runs completed (15.8%). 
Progress: 102/640 runs completed (15.9%). 
Progress: 103/640 runs completed (16.1%). 
Progress: 104/640 runs completed (16.2%). 
Progress: 105/640 runs completed (16.4%). 
Progress: 106/640 runs completed (16.6%). 
Progress: 107/640 runs completed (16.7%). 
Progress: 108/640 runs completed (16.9%). 
Progress: 109/640 runs completed (17.0%). 
Progress: 110/640 runs completed (17.2%). 
Progress: 111/640 runs completed (17.3%). 
Progress: 112/640 runs completed (17.5%). 
Progress: 113/640 runs completed (17.7%). 
Progress: 114/640 runs completed (17.8%). 
Progress: 115/640 runs completed (18.0%). 
Progress: 116/640 runs completed (18.1%). 
Progress: 117/640 runs completed (18.3%). 
Progress: 118/640 runs completed (18.4%). 
Progress: 119/640 runs completed (18.6%). 
Progress: 120/640 runs completed (18.8%). 
Progress: 121/640 runs completed (18.9%). 
Progress: 122/640 runs completed (19.1%). 
Progress: 123/640 runs completed (19.2%). 
Progress: 124/640 runs completed (19.4%). 
Progress: 125/640 runs completed (19.5%). 
Progress: 126/640 runs completed (19.7%). 
Progress: 127/640 runs completed (19.8%). 
Progress: 128/640 runs completed (20.0%). 
Progress: 129/640 runs completed (20.2%). 
Progress: 130/640 runs completed (20.3%). 
Progress: 131/640 runs completed (20.5%). 
Progress: 132/640 runs completed (20.6%). 
Progress: 133/640 runs completed (20.8%). 
Progress: 134/640 runs completed (20.9%). 
Progress: 135/640 runs completed (21.1%). 
Progress: 136/640 runs completed (21.2%). 
Progress: 137/640 runs completed (21.4%). 
Progress: 138/640 runs completed (21.6%). 
Progress: 139/640 runs completed (21.7%). 
Progress: 140/640 runs completed (21.9%). 
Progress: 141/640 runs completed (22.0%). 
Progress: 142/640 runs completed (22.2%). 
Progress: 143/640 runs completed (22.3%). 
Progress: 144/640 runs completed (22.5%). 
Progress: 145/640 runs completed (22.7%). 
Progress: 146/640 runs completed (22.8%). 
Progress: 147/640 runs completed (23.0%). 
Progress: 148/640 runs completed (23.1%). 
Progress: 149/640 runs completed (23.3%). 
Progress: 150/640 runs completed (23.4%). 
Progress: 151/640 runs completed (23.6%). 
Progress: 152/640 runs completed (23.8%). 
Progress: 153/640 runs completed (23.9%). 
Progress: 154/640 runs completed (24.1%). 
Progress: 155/640 runs completed (24.2%). 
Progress: 156/640 runs completed (24.4%). 
Progress: 157/640 runs completed (24.5%). 
Progress: 158/640 runs completed (24.7%). 
Progress: 159/640 runs completed (24.8%). 
Progress: 160/640 runs completed (25.0%). 
Progress: 161/640 runs completed (25.2%). 
Progress: 162/640 runs completed (25.3%). 
Progress: 163/640 runs completed (25.5%). 
Progress: 164/640 runs completed (25.6%). 
Progress: 165/640 runs completed (25.8%). 
Progress: 166/640 runs completed (25.9%). 
Progress: 167/640 runs completed (26.1%). 
Progress: 168/640 runs completed (26.2%). 
Progress: 169/640 runs completed (26.4%). 
Progress: 170/640 runs completed (26.6%). 
Progress: 171/640 runs completed (26.7%). 
Progress: 172/640 runs completed (26.9%). 
Progress: 173/640 runs completed (27.0%). 
Progress: 174/640 runs completed (27.2%). 
Progress: 175/640 runs completed (27.3%). 
Progress: 176/640 runs completed (27.5%). 
Progress: 177/640 runs completed (27.7%). 
Progress: 178/640 runs completed (27.8%). 
Progress: 179/640 runs completed (28.0%). 
Progress: 180/640 runs completed (28.1%). 
Progress: 181/640 runs completed (28.3%). 
Progress: 182/640 runs completed (28.4%). 
Progress: 183/640 runs completed (28.6%). 
Progress: 184/640 runs completed (28.7%). 
Progress: 185/640 runs completed (28.9%). 
Progress: 186/640 runs completed (29.1%). 
Progress: 187/640 runs completed (29.2%). 
Progress: 188/640 runs completed (29.4%). 
Progress: 189/640 runs completed (29.5%). 
Progress: 190/640 runs completed (29.7%). 
Progress: 191/640 runs completed (29.8%). 
Progress: 192/640 runs completed (30.0%). 
Progress: 193/640 runs completed (30.2%). 
Progress: 194/640 runs completed (30.3%). 
Progress: 195/640 runs completed (30.5%). 
Progress: 196/640 runs completed (30.6%). 
Progress: 197/640 runs completed (30.8%). 
Progress: 198/640 runs completed (30.9%). 
Progress: 199/640 runs completed (31.1%). 
Progress: 200/640 runs completed (31.2%). 
Progress: 201/640 runs completed (31.4%). 
Progress: 202/640 runs completed (31.6%). 
Progress: 203/640 runs completed (31.7%). 
Progress: 204/640 runs completed (31.9%). 
Progress: 205/640 runs completed (32.0%). 
Progress: 206/640 runs completed (32.2%). 
Progress: 207/640 runs completed (32.3%). 
Progress: 208/640 runs completed (32.5%). 
Progress: 209/640 runs completed (32.7%). 
Progress: 210/640 runs completed (32.8%). 
Progress: 211/640 runs completed (33.0%). 
Progress: 212/640 runs completed (33.1%). 
Progress: 213/640 runs completed (33.3%). 
Progress: 214/640 runs completed (33.4%). 
Progress: 215/640 runs completed (33.6%). 
Progress: 216/640 runs completed (33.8%). 
Progress: 217/640 runs completed (33.9%). 
Progress: 218/640 runs completed (34.1%). 
Progress: 219/640 runs completed (34.2%). 
Progress: 220/640 runs completed (34.4%). 
Progress: 221/640 runs completed (34.5%). 
Progress: 222/640 runs completed (34.7%). 
Progress: 223/640 runs completed (34.8%). 
Progress: 224/640 runs completed (35.0%). 
Progress: 225/640 runs completed (35.2%). 
Progress: 226/640 runs completed (35.3%). 
Progress: 227/640 runs completed (35.5%). 
Progress: 228/640 runs completed (35.6%). 
Progress: 229/640 runs completed (35.8%). 
Progress: 230/640 runs completed (35.9%). 
Progress: 231/640 runs completed (36.1%). 
Progress: 232/640 runs completed (36.2%). 
Progress: 233/640 runs completed (36.4%). 
Progress: 234/640 runs completed (36.6%). 
Progress: 235/640 runs completed (36.7%). 
Progress: 236/640 runs completed (36.9%). 
Progress: 237/640 runs completed (37.0%). 
Progress: 238/640 runs completed (37.2%). 
Progress: 239/640 runs completed (37.3%). 
Progress: 240/640 runs completed (37.5%). 
Progress: 241/640 runs completed (37.7%). 
Progress: 242/640 runs completed (37.8%). 
Progress: 243/640 runs completed (38.0%). 
Progress: 244/640 runs completed (38.1%). 
Progress: 245/640 runs completed (38.3%). 
Progress: 246/640 runs completed (38.4%). 
Progress: 247/640 runs completed (38.6%). 
Progress: 248/640 runs completed (38.8%). 
Progress: 249/640 runs completed (38.9%). 
Progress: 250/640 runs completed (39.1%). 
Progress: 251/640 runs completed (39.2%). 
Progress: 252/640 runs completed (39.4%). 
Progress: 253/640 runs completed (39.5%). 
Progress: 254/640 runs completed (39.7%). 
Progress: 255/640 runs completed (39.8%). 
Progress: 256/640 runs completed (40.0%). 
Progress: 257/640 runs completed (40.2%). 
Progress: 258/640 runs completed (40.3%). 
Progress: 259/640 runs completed (40.5%). 
Progress: 260/640 runs completed (40.6%). 
Progress: 261/640 runs completed (40.8%). 
Progress: 262/640 runs completed (40.9%). 
Progress: 263/640 runs completed (41.1%). 
Progress: 264/640 runs completed (41.2%). 
Progress: 265/640 runs completed (41.4%). 
Progress: 266/640 runs completed (41.6%). 
Progress: 267/640 runs completed (41.7%). 
Progress: 268/640 runs completed (41.9%). 
Progress: 269/640 runs completed (42.0%). 
Progress: 270/640 runs completed (42.2%). 
Progress: 271/640 runs completed (42.3%). 
Progress: 272/640 runs completed (42.5%). 
Progress: 273/640 runs completed (42.7%). 
Progress: 274/640 runs completed (42.8%). 
Progress: 275/640 runs completed (43.0%). 
Progress: 276/640 runs completed (43.1%). 
Progress: 277/640 runs completed (43.3%). 
Progress: 278/640 runs completed (43.4%). 
Progress: 279/640 runs completed (43.6%). 
Progress: 280/640 runs completed (43.8%). 
Progress: 281/640 runs completed (43.9%). 
Progress: 282/640 runs completed (44.1%). 
Progress: 283/640 runs completed (44.2%). 
Progress: 284/640 runs completed (44.4%). 
Progress: 285/640 runs completed (44.5%). 
Progress: 286/640 runs completed (44.7%). 
Progress: 287/640 runs completed (44.8%). 
Progress: 288/640 runs completed (45.0%). 
Progress: 289/640 runs completed (45.2%). 
Progress: 290/640 runs completed (45.3%). 
Progress: 291/640 runs completed (45.5%). 
Progress: 292/640 runs completed (45.6%). 
Progress: 293/640 runs completed (45.8%). 
Progress: 294/640 runs completed (45.9%). 
Progress: 295/640 runs completed (46.1%). 
Progress: 296/640 runs completed (46.2%). 
Progress: 297/640 runs completed (46.4%). 
Progress: 298/640 runs completed (46.6%). 
Progress: 299/640 runs completed (46.7%). 
Progress: 300/640 runs completed (46.9%). 
Progress: 301/640 runs completed (47.0%). 
Progress: 302/640 runs completed (47.2%). 
Progress: 303/640 runs completed (47.3%). 
Progress: 304/640 runs completed (47.5%). 
Progress: 305/640 runs completed (47.7%). 
Progress: 306/640 runs completed (47.8%). 
Progress: 307/640 runs completed (48.0%). 
Progress: 308/640 runs completed (48.1%). 
Progress: 309/640 runs completed (48.3%). 
Progress: 310/640 runs completed (48.4%). 
Progress: 311/640 runs completed (48.6%). 
Progress: 312/640 runs completed (48.8%). 
Progress: 313/640 runs completed (48.9%). 
Progress: 314/640 runs completed (49.1%). 
Progress: 315/640 runs completed (49.2%). 
Progress: 316/640 runs completed (49.4%). 
Progress: 317/640 runs completed (49.5%). 
Progress: 318/640 runs completed (49.7%). 
Progress: 319/640 runs completed (49.8%). 
Progress: 320/640 runs completed (50.0%). 
Progress: 321/640 runs completed (50.2%). 
Progress: 322/640 runs completed (50.3%). 
Progress: 323/640 runs completed (50.5%). 
Progress: 324/640 runs completed (50.6%). 
Progress: 325/640 runs completed (50.8%). 
Progress: 326/640 runs completed (50.9%). 
Progress: 327/640 runs completed (51.1%). 
Progress: 328/640 runs completed (51.2%). 
Progress: 329/640 runs completed (51.4%). 
Progress: 330/640 runs completed (51.6%). 
Progress: 331/640 runs completed (51.7%). 
Progress: 332/640 runs completed (51.9%). 
Progress: 333/640 runs completed (52.0%). 
Progress: 334/640 runs completed (52.2%). 
Progress: 335/640 runs completed (52.3%). 
Progress: 336/640 runs completed (52.5%). 
Progress: 337/640 runs completed (52.7%). 
Progress: 338/640 runs completed (52.8%). 
Progress: 339/640 runs completed (53.0%). 
Progress: 340/640 runs completed (53.1%). 
Progress: 341/640 runs completed (53.3%). 
Progress: 342/640 runs completed (53.4%). 
Progress: 343/640 runs completed (53.6%). 
Progress: 344/640 runs completed (53.8%). 
Progress: 345/640 runs completed (53.9%). 
Progress: 346/640 runs completed (54.1%). 
Progress: 347/640 runs completed (54.2%). 
Progress: 348/640 runs completed (54.4%). 
Progress: 349/640 runs completed (54.5%). 
Progress: 350/640 runs completed (54.7%). 
Progress: 351/640 runs completed (54.8%). 
Progress: 352/640 runs completed (55.0%). 
Progress: 353/640 runs completed (55.2%). 
Progress: 354/640 runs completed (55.3%). 
Progress: 355/640 runs completed (55.5%). 
Progress: 356/640 runs completed (55.6%). 
Progress: 357/640 runs completed (55.8%). 
Progress: 358/640 runs completed (55.9%). 
Progress: 359/640 runs completed (56.1%). 
Progress: 360/640 runs completed (56.2%). 
Progress: 361/640 runs completed (56.4%). 
Progress: 362/640 runs completed (56.6%). 
Progress: 363/640 runs completed (56.7%). 
Progress: 364/640 runs completed (56.9%). 
Progress: 365/640 runs completed (57.0%). 
Progress: 366/640 runs completed (57.2%). 
Progress: 367/640 runs completed (57.3%). 
Progress: 368/640 runs completed (57.5%). 
Progress: 369/640 runs completed (57.7%). 
Progress: 370/640 runs completed (57.8%). 
Progress: 371/640 runs completed (58.0%). 
Progress: 372/640 runs completed (58.1%). 
Progress: 373/640 runs completed (58.3%). 
Progress: 374/640 runs completed (58.4%). 
Progress: 375/640 runs completed (58.6%). 
Progress: 376/640 runs completed (58.8%). 
Progress: 377/640 runs completed (58.9%). 
Progress: 378/640 runs completed (59.1%). 
Progress: 379/640 runs completed (59.2%). 
Progress: 380/640 runs completed (59.4%). 
Progress: 381/640 runs completed (59.5%). 
Progress: 382/640 runs completed (59.7%). 
Progress: 383/640 runs completed (59.8%). 
Progress: 384/640 runs completed (60.0%). 
Progress: 385/640 runs completed (60.2%). 
Progress: 386/640 runs completed (60.3%). 
Progress: 387/640 runs completed (60.5%). 
Progress: 388/640 runs completed (60.6%). 
Progress: 389/640 runs completed (60.8%). 
Progress: 390/640 runs completed (60.9%). 
Progress: 391/640 runs completed (61.1%). 
Progress: 392/640 runs completed (61.3%). 
Progress: 393/640 runs completed (61.4%). 
Progress: 394/640 runs completed (61.6%). 
Progress: 395/640 runs completed (61.7%). 
Progress: 396/640 runs completed (61.9%). 
Progress: 397/640 runs completed (62.0%). 
Progress: 398/640 runs completed (62.2%). 
Progress: 399/640 runs completed (62.3%). 
Progress: 400/640 runs completed (62.5%). 
Progress: 401/640 runs completed (62.7%). 
Progress: 402/640 runs completed (62.8%). 
Progress: 403/640 runs completed (63.0%). 
Progress: 404/640 runs completed (63.1%). 
Progress: 405/640 runs completed (63.3%). 
Progress: 406/640 runs completed (63.4%). 
Progress: 407/640 runs completed (63.6%). 
Progress: 408/640 runs completed (63.7%). 
Progress: 409/640 runs completed (63.9%). 
Progress: 410/640 runs completed (64.1%). 
Progress: 411/640 runs completed (64.2%). 
Progress: 412/640 runs completed (64.4%). 
Progress: 413/640 runs completed (64.5%). 
Progress: 414/640 runs completed (64.7%). 
Progress: 415/640 runs completed (64.8%). 
Progress: 416/640 runs completed (65.0%). 