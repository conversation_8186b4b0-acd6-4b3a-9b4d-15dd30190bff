import numpy as np
import os

def generate_custom(n, epsilon):
    """
    Generates a single problem instance for the 'Uniform' type.

    Args:
        n (int): The number of elements.
        epsilon (float): The error parameter for prediction perturbation.

    Returns:
        tuple: A tuple containing (actual_values, predicted_values).
    """
    # 1. Generate actual values: v(i) ~ Exponential(1)
    # The 'scale' parameter in numpy.random.exponential is 1/lambda.
    # For lambda=1, scale=1.

    actual_values = [100000, 10, 8]
    predicted_values = [1, 10, 3]
    for x in range(epsilon):
        actual_values.append(8+0.0001*(x+1))
        predicted_values.append(3+0.0001*(x+1))

    # 2. Generate predicted values: v_hat(i) = delta_i * v(i)
    # where delta_i is sampled uniformly from [1 - epsilon, 1 + epsilon]

    return actual_values, predicted_values

def save_instance_to_file(n, actual_values, predicted_values, filepath):
    """
    Saves the generated instance to a .txt file in the specified format.

    Args:
        n (int): The number of elements.
        actual_values (np.array): Array of actual values.
        predicted_values (np.array): Array of predicted values.
        filepath (str): The path to the output file.
    """
    with open(filepath, 'w') as f:
        # Line 1: N
        f.write(f"{n}\n")

        # Line 2: N numbers representing elements' real values
        actual_values_str = " ".join(map(str, actual_values))
        f.write(f"{actual_values_str}\n")

        # Line 3: N numbers representing elements' predicted values
        predicted_values_str = " ".join(map(str, predicted_values))
        f.write(f"{predicted_values_str}\n")
def main():
    """
    Main function to generate all required test cases.
    """
    N = 3
    # --- Parameters ---
    k_values = [1]
    # Epsilon values from 0.0 to 0.9. The case e=1 is omitted.
    N_values = np.arange(0, 50, 5)

    # --- Directory Setup ---
    output_dir = "custom_test_cases"
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
        print(f"Created directory: {output_dir}")
    # --- Generation Loop ---
    NUM_ATTEMPTS_PER_CASE = 10
    total_files = len(k_values) * len(N_values) * NUM_ATTEMPTS_PER_CASE
    print(f"Generating {total_files} test files for N={N} ({NUM_ATTEMPTS_PER_CASE} attempts per case)...")

    for k in k_values:
        for extra in N_values:
            for attempt in range(NUM_ATTEMPTS_PER_CASE):
                # Generate the data

                actual_v, predicted_v = generate_custom(N, extra)

                # Create a descriptive filename that includes k and the attempt number
                filename = f"custom_n{N}_k{k}_eps{extra:.3f}_{attempt+1}.txt"
                filepath = os.path.join(output_dir, filename)

                # Save the data to the file
                save_instance_to_file(len(actual_v), actual_v, predicted_v, filepath)
            
            print(f"Generated {NUM_ATTEMPTS_PER_CASE} files for k={k}, epsilon={extra:.3f}")

    print("\nAll test cases generated.")

if __name__ == "__main__":
    main()
