import numpy as np
import os

def generate_decoy_error_instance(n, spike_value, spike_epsilon, decoy_epsilon):
    """
    Generates a deception instance by applying a parameterized error to the decoy.

    Args:
        n (int): The number of elements.
        spike_value (float): The actual value of the best element.
        spike_epsilon (float): The prediction error for the true spike.
        decoy_epsilon (float): The multiplicative prediction error for the decoy.

    Returns:
        tuple: A tuple containing (actual_values, predicted_values).
    """
    # 1. Initialize background elements.
    actual_values = np.random.uniform(low=1.0, high=5.0, size=n)
    predicted_values = actual_values * np.random.uniform(0.95, 1.05, size=n)

    # 2. Choose indices for the spike and the decoy.
    indices = np.arange(n)
    true_best_idx, decoy_idx = np.random.choice(indices, size=2, replace=False)
    
    # 3. Set the spike's actual and predicted values.
    actual_values[true_best_idx] = spike_value
    spike_prediction = spike_value * (1.0 - spike_epsilon)
    predicted_values[true_best_idx] = spike_prediction
    
    # 4. Set the decoy's predicted value based on its actual value and error.
    decoy_actual_value = actual_values[decoy_idx]
    decoy_prediction = decoy_actual_value * (1.0 + decoy_epsilon)
    predicted_values[decoy_idx] = decoy_prediction
    
    return actual_values, predicted_values

def save_instance_to_file(n, actual_values, predicted_values, filepath):
    """Saves the generated instance to a .txt file."""
    def format_num(x):
        return str(int(x)) if x == int(x) else f"{x:.4f}"

    with open(filepath, 'w') as f:
        f.write(f"{n}\n")
        f.write(" ".join(map(format_num, actual_values)) + "\n")
        f.write(" ".join(map(format_num, predicted_values)) + "\n")

def main():
    """Main function to generate a structured set of test cases."""
    # --- Base Parameters ---
    N = 100
    SPIKE_VALUE = 500.0
    SPIKE_EPSILON = 0.1 

    # --- Granular Parameter to Vary ---
    # A mix of small, medium (critical), and large errors for the decoy.
    decoy_epsilon_values = [
        0.0,      # Case 1: No decoy error (prediction is accurate)
        20.0,     # Case 2: Small error (deception will fail)
        80.0,     # Case 3: Error below the critical threshold (deception likely fails)
        120.0,    # Case 4: Error in the critical zone (deception might succeed)
        180.0,    # Case 5: Error in the critical zone (deception likely succeeds)
        300.0,    # Case 6: Error above the critical threshold (deception almost guaranteed)
        500.0     # Case 7: Very large error (deception guaranteed)
    ]

    # --- Directory Setup ---
    output_dir = "granular_decoy_error_cases"
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
        print(f"Created directory: {output_dir}")

    # --- Explanatory Printout ---
    spike_pred_val = SPIKE_VALUE * (1.0 - SPIKE_EPSILON)
    print("--- Test Case Generation ---")
    print(f"Spike's Actual Value: {SPIKE_VALUE}")
    print(f"Spike's Predicted Value: {spike_pred_val:.2f}")
    print("The deception works if a decoy (actual value 1-5) has a prediction > 450.")
    print("The critical decoy error (eps_d) is between ~90 and ~300.")
    print("-" * 30)

    # --- Generation Loop ---
    print(f"Generating {len(decoy_epsilon_values)} test files for N={N}...")

    for i, decoy_eps in enumerate(decoy_epsilon_values):
        actual_v, predicted_v = generate_decoy_error_instance(
            N, SPIKE_VALUE, SPIKE_EPSILON, decoy_eps
        )

        filename = f"case_{i+1:02d}_decoyeps{decoy_eps:.1f}.txt"
        filepath = os.path.join(output_dir, filename)

        save_instance_to_file(N, actual_v, predicted_v, filepath)
        print(f"Successfully generated: {filepath}")

    print("\nAll test cases generated.")

if __name__ == "__main__":
    main()