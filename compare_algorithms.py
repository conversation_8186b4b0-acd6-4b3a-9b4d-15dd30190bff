import numpy as np
from main import secretary_problem
from learned_dynkin import learned_dynkin_algorithm

def run_comparison(num_simulations=1000, num_candidates=100):
    """
    Runs simulations to compare the performance of two secretary problem algorithms.
    """
    # Performance trackers
    ordered_dynkin_results = {'hired_values': [], 'is_best': []}
    learned_dynkin_results = {'hired_values': [], 'is_best': []}

    print(f"Running {num_simulations} simulations with {num_candidates} candidates each...")

    for _ in range(num_simulations):
        # --- Generate consistent data for a single simulation ---
        candidates = list(range(num_candidates))
        v_actual = {i: np.random.rand() for i in candidates}
        v_predictions = {i: v_actual[i] + np.random.normal(0, 0.1) for i in candidates}
        best_possible_value = max(v_actual.values())
        best_candidate = max(v_actual, key=v_actual.get)

        # --- Run Algorithm 1: Ordered Learned Dynkin ---
        # Arrival times are now generated internally by the function
        hired_ordered, _ = secretary_problem(v_predictions, v_actual)
        ordered_dynkin_results['hired_values'].append(v_actual[hired_ordered])
        ordered_dynkin_results['is_best'].append(hired_ordered == best_candidate)

        # --- Run Algorithm 2: Learned Dynkin ---
        hired_learned, _ = learned_dynkin_algorithm(v_predictions, v_actual)
        learned_dynkin_results['hired_values'].append(v_actual[hired_learned])
        learned_dynkin_results['is_best'].append(hired_learned == best_candidate)

    # --- Calculate and Print Results ---
    avg_val_ordered = np.mean(ordered_dynkin_results['hired_values'])
    pct_best_ordered = np.mean(ordered_dynkin_results['is_best']) * 100
    
    avg_val_learned = np.mean(learned_dynkin_results['hired_values'])
    pct_best_learned = np.mean(learned_dynkin_results['is_best']) * 100

    print("\n--- Algorithm Performance Comparison ---")
    print(f"{'Metric':<25} {'Ordered Learned Dynkin':<25} {'Learned Dynkin':<25}")
    print("-" * 75)
    print(f"{'Avg. Hired Value':<25} {avg_val_ordered:<25.4f} {avg_val_learned:<25.4f}")
    print(f"{'Success Rate (% Best)':<25} {pct_best_ordered:<25.2f}% {pct_best_learned:<25.2f}%")
    print("-" * 75)

if __name__ == '__main__':
    run_comparison()
