Starting optimization (Revised Case 4, includes p4)...
Theta = 0.628, Const Factor = 0.2285
Initial Guess: [0.37, 0.4, 0.1, 0.5, 0.5]
Bounds: [(1e-06, 0.999999), (0.0, 1.0), (0.0, 1.0), (0.999, 1.0), (0.999, 1.0)]
Constraints: p0 + p1 <= 1
------------------------------
Optimization terminated successfully    (Exit mode 0)
            Current function value: -0.22909100427690782
            Iterations: 18
            Function evaluations: 158
            Gradient evaluations: 18
------------------------------
Optimization Successful!
Optimal tau: 0.388759
Optimal p0:  0.541536
Optimal p1:  0.083667
Optimal p3:  0.999998
Optimal p4:  1.000000
Prob T=1:    0.374797
Maximum Minimum Competitive Ratio: 0.229091
------------------------------
Competitive ratios for each case at optimum:
Case 1/2: 0.229372
Case 3:   0.336567 (worst m in range(2, 21))
Case 4:   0.229091 (worst m in range(1, 21))
Case 5:   0.229091 (worst m in range(1, 21))
Case 6:   0.253019 (worst m in range(1, 21))
Min Check:0.229091 (should match max_min_ratio)
